('/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/PYZ-00.pyz',
 [('PyQt6',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/__init__.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_compression.py',
   'PYMODULE'),
  ('_py_abc',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_strptime',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/argparse.py',
   'PYMODULE'),
  ('ast',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/ast.py',
   'PYMODULE'),
  ('base64',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/base64.py',
   'PYMODULE'),
  ('bisect',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/calendar.py',
   'PYMODULE'),
  ('contextlib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/copy.py',
   'PYMODULE'),
  ('csv',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/csv.py',
   'PYMODULE'),
  ('dataclasses',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/decimal.py',
   'PYMODULE'),
  ('dis',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/dis.py',
   'PYMODULE'),
  ('email',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/fractions.py',
   'PYMODULE'),
  ('getopt',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/getopt.py',
   'PYMODULE'),
  ('gettext',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/gettext.py',
   'PYMODULE'),
  ('gzip',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/hashlib.py',
   'PYMODULE'),
  ('importlib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/inspect.py',
   'PYMODULE'),
  ('logging',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lzma.py',
   'PYMODULE'),
  ('numbers',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/opcode.py',
   'PYMODULE'),
  ('optparse',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/optparse.py',
   'PYMODULE'),
  ('pathlib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/pathlib.py',
   'PYMODULE'),
  ('pickle',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/pickle.py',
   'PYMODULE'),
  ('pkgutil',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/pkgutil.py',
   'PYMODULE'),
  ('pprint',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/py_compile.py',
   'PYMODULE'),
  ('pysqlcipher3',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/pysqlcipher3/__init__.py',
   'PYMODULE'),
  ('pysqlcipher3.dbapi2',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/pysqlcipher3/dbapi2.py',
   'PYMODULE'),
  ('quopri',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/quopri.py',
   'PYMODULE'),
  ('random',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/random.py',
   'PYMODULE'),
  ('selectors',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/selectors.py',
   'PYMODULE'),
  ('shutil',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/signal.py',
   'PYMODULE'),
  ('socket',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/socket.py',
   'PYMODULE'),
  ('statistics',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/statistics.py',
   'PYMODULE'),
  ('string',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/subprocess.py',
   'PYMODULE'),
  ('tarfile',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/tarfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/threading.py',
   'PYMODULE'),
  ('token',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/tracemalloc.py',
   'PYMODULE'),
  ('typing',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/typing.py',
   'PYMODULE'),
  ('urllib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/urllib/parse.py',
   'PYMODULE'),
  ('uu',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/uu.py',
   'PYMODULE'),
  ('zipfile',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/zipfile.py',
   'PYMODULE'),
  ('zipimport',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/zipimport.py',
   'PYMODULE')])
