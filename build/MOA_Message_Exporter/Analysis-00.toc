(['/Users/<USER>/works/coderepository/local/rag/read_messages_from_moa/read_moa_message_gui_pyqt.py'],
 ['/Users/<USER>/works/coderepository/local/rag/read_messages_from_moa'],
 [],
 [('/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.10.0 (default, Mar  3 2022, 03:54:28) [Clang 12.0.0 ]',
 [('pyi_rth_inspect',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('read_moa_message_gui_pyqt',
   '/Users/<USER>/works/coderepository/local/rag/read_messages_from_moa/read_moa_message_gui_pyqt.py',
   'PYSOURCE')],
 [('pkgutil',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/zipimport.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/readers.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/abc.py',
   'PYMODULE'),
  ('typing',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/typing.py',
   'PYMODULE'),
  ('contextlib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/contextlib.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/quoprimime.py',
   'PYMODULE'),
  ('string',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/string.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/urllib/__init__.py',
   'PYMODULE'),
  ('email.iterators',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/generator.py',
   'PYMODULE'),
  ('copy',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/copy.py',
   'PYMODULE'),
  ('random',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/random.py',
   'PYMODULE'),
  ('statistics',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_compat_pickle.py',
   'PYMODULE'),
  ('struct',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/struct.py',
   'PYMODULE'),
  ('threading',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_threading_local.py',
   'PYMODULE'),
  ('bisect',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/bisect.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/base64.py',
   'PYMODULE'),
  ('getopt',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/getopt.py',
   'PYMODULE'),
  ('gettext',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/gettext.py',
   'PYMODULE'),
  ('email.charset',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/calendar.py',
   'PYMODULE'),
  ('argparse',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/argparse.py',
   'PYMODULE'),
  ('shutil',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/shutil.py',
   'PYMODULE'),
  ('tarfile',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/gzip.py',
   'PYMODULE'),
  ('_compression',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_compression.py',
   'PYMODULE'),
  ('lzma',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/bz2.py',
   'PYMODULE'),
  ('fnmatch',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/fnmatch.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/urllib/parse.py',
   'PYMODULE'),
  ('socket',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/socket.py',
   'PYMODULE'),
  ('selectors',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/selectors.py',
   'PYMODULE'),
  ('quopri',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/quopri.py',
   'PYMODULE'),
  ('uu',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/uu.py',
   'PYMODULE'),
  ('optparse',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/optparse.py',
   'PYMODULE'),
  ('textwrap',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/textwrap.py',
   'PYMODULE'),
  ('email',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/email/feedparser.py',
   'PYMODULE'),
  ('csv',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/csv.py',
   'PYMODULE'),
  ('tokenize',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/tokenize.py',
   'PYMODULE'),
  ('token',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/token.py',
   'PYMODULE'),
  ('pathlib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/pathlib.py',
   'PYMODULE'),
  ('zipfile',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/zipfile.py',
   'PYMODULE'),
  ('py_compile',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/py_compile.py',
   'PYMODULE'),
  ('inspect',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/inspect.py',
   'PYMODULE'),
  ('dis',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/dis.py',
   'PYMODULE'),
  ('opcode',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/opcode.py',
   'PYMODULE'),
  ('ast',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/ast.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.util',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/util.py',
   'PYMODULE'),
  ('importlib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/importlib/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_py_abc',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/stringprep.py',
   'PYMODULE'),
  ('pysqlcipher3.dbapi2',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/pysqlcipher3/dbapi2.py',
   'PYMODULE'),
  ('pysqlcipher3',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/pysqlcipher3/__init__.py',
   'PYMODULE'),
  ('PyQt6',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/__init__.py',
   'PYMODULE'),
  ('_strptime',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/_strptime.py',
   'PYMODULE'),
  ('subprocess',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/subprocess.py',
   'PYMODULE'),
  ('signal',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/signal.py',
   'PYMODULE'),
  ('datetime',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/datetime.py',
   'PYMODULE')],
 [('libpython3.10.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/libpython3.10.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   'BINARY'),
  ('lib-dynload/zlib.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/zlib.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/binascii.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_statistics.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_contextvars.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_decimal.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_pickle.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_struct.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_hashlib.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_sha3.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_blake2.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_sha256.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_md5.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_sha1.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_sha512.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_random.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_bisect.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/math.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/grp.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_lzma.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_bz2.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/unicodedata.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/array.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/select.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_socket.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_csv.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/resource.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_opcode.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_heapq.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_multibytecodec.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_jp.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_kr.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_iso2022.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_cn.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_tw.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_hk.cpython-310-darwin.so',
   'EXTENSION'),
  ('pysqlcipher3/_sqlite3.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/pysqlcipher3/_sqlite3.cpython-310-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtCore.abi3.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/QtCore.abi3.so',
   'EXTENSION'),
  ('PyQt6/sip.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/sip.cpython-310-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtWidgets.abi3.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtGui.abi3.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtDBus.abi3.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/QtDBus.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_posixsubprocess.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/fcntl.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_datetime.cpython-310-darwin.so',
   'EXTENSION'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   'BINARY'),
  ('libz.1.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/libz.1.dylib',
   'BINARY'),
  ('libcrypto.1.1.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/libcrypto.1.1.dylib',
   'BINARY'),
  ('liblzma.5.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/liblzma.5.dylib',
   'BINARY'),
  ('libbz2.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/libbz2.dylib',
   'BINARY'),
  ('libsqlcipher.0.dylib',
   '/opt/homebrew/opt/sqlcipher/lib/libsqlcipher.0.dylib',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libcrypto.3.dylib',
   'BINARY')],
 [],
 [],
 [('PyQt6/Qt6/translations/qt_help_de.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hu.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_fr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ca.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ja.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_da.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_en.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ru.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_es.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_da.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_gd.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lv.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ko.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_TW.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nn.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fi.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_en.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sv.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_sv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_es.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_gl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ko.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ru.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_bg.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ka.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hu.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_de.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_cs.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_da.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ar.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fa.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_lv.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ru.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nn.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_tr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_tr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_uk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ar.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_BR.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ja.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_bg.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nn.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_es.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_de.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_he.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ka.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ca.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_it.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fi.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ka.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ca.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hu.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_he.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ko.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_uk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_bg.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fa.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_uk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lt.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_lt.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_it.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_tr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_PT.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ar.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gd.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_gl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_CN.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ja.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_cs.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_sk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_cs.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_it.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_en.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_en.qm',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/base_library.zip',
   'DATA'),
  ('QtGui', 'PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui', 'SYMLINK'),
  ('QtCore', 'PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore', 'SYMLINK'),
  ('QtWidgets',
   'PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'SYMLINK'),
  ('QtSvg', 'PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg', 'SYMLINK'),
  ('QtNetwork',
   'PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'SYMLINK'),
  ('QtPdf', 'PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf', 'SYMLINK'),
  ('QtDBus', 'PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/QtPdf', 'Versions/Current/QtPdf', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/Current', 'A', 'SYMLINK')])
