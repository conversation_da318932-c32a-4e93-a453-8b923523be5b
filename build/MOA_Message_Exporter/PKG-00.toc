('/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/MOA_Message_Exporter.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_struct.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/zlib.cpython-310-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('read_moa_message_gui_pyqt',
   '/Users/<USER>/works/coderepository/local/rag/read_messages_from_moa/read_moa_message_gui_pyqt.py',
   'PYSOURCE'),
  ('libpython3.10.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/libpython3.10.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/plugins/imageformats/libqpdf.dylib',
   'BINARY'),
  ('lib-dynload/binascii.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/binascii.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_statistics.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_contextvars.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_decimal.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_pickle.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_hashlib.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_sha3.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_blake2.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_sha256.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_md5.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_sha1.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_sha512.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_random.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_bisect.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/math.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/grp.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_lzma.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_bz2.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/unicodedata.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/array.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/select.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_socket.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_csv.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/resource.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_opcode.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_heapq.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_multibytecodec.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_jp.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_kr.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_iso2022.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_cn.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_tw.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_codecs_hk.cpython-310-darwin.so',
   'EXTENSION'),
  ('pysqlcipher3/_sqlite3.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/pysqlcipher3/_sqlite3.cpython-310-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtCore.abi3.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/QtCore.abi3.so',
   'EXTENSION'),
  ('PyQt6/sip.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/sip.cpython-310-darwin.so',
   'EXTENSION'),
  ('PyQt6/QtWidgets.abi3.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtGui.abi3.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt6/QtDBus.abi3.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/QtDBus.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_posixsubprocess.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/fcntl.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-310-darwin.so',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/lib-dynload/_datetime.cpython-310-darwin.so',
   'EXTENSION'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf',
   'BINARY'),
  ('libz.1.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/libz.1.dylib',
   'BINARY'),
  ('libcrypto.1.1.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/libcrypto.1.1.dylib',
   'BINARY'),
  ('liblzma.5.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/liblzma.5.dylib',
   'BINARY'),
  ('libbz2.dylib',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/libbz2.dylib',
   'BINARY'),
  ('libsqlcipher.0.dylib',
   '/opt/homebrew/opt/sqlcipher/lib/libsqlcipher.0.dylib',
   'BINARY'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('PyQt6/Qt6/translations/qt_help_de.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hu.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_fr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ca.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ja.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_da.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_en.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ru.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_es.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_da.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_gd.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lv.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ko.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_TW.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nn.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fi.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_en.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_sv.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_sv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_es.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_gl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ko.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ru.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_bg.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ka.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hu.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_nl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_de.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_cs.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_da.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ar.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fa.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_lv.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ru.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ru.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nn.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_tr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_tr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_uk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ar.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_BR.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ja.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_bg.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nn.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_nn.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_es.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_es.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_de.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_he.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ka.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ca.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_pl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_it.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_sk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_fi.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_fi.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_hr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_ka.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_ka.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ca.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ca.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_nl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_hu.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_hu.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_hr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_he.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_ko.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_ko.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_uk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_bg.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_fa.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_uk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_nl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_nl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_lt.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_lt.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_it.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_tr.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_tr.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_pt_PT.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ar.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_gd.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_gd.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_gl.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_zh_CN.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_ja.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_cs.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_sk.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qtbase_cs.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_it.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt6/Qt6/translations/qt_help_en.qm',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/translations/qt_help_en.qm',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/works/coderepository/local/rag/build/MOA_Message_Exporter/base_library.zip',
   'DATA'),
  ('QtGui', 'PyQt6/Qt6/lib/QtGui.framework/Versions/A/QtGui', 'SYMLINK'),
  ('QtCore', 'PyQt6/Qt6/lib/QtCore.framework/Versions/A/QtCore', 'SYMLINK'),
  ('QtWidgets',
   'PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'SYMLINK'),
  ('QtSvg', 'PyQt6/Qt6/lib/QtSvg.framework/Versions/A/QtSvg', 'SYMLINK'),
  ('QtNetwork',
   'PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'SYMLINK'),
  ('QtPdf', 'PyQt6/Qt6/lib/QtPdf.framework/Versions/A/QtPdf', 'SYMLINK'),
  ('QtDBus', 'PyQt6/Qt6/lib/QtDBus.framework/Versions/A/QtDBus', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtCore.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtDBus.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtGui.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtNetwork.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/QtPdf', 'Versions/Current/QtPdf', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtPdf.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtPdf.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtSvg.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   '/Users/<USER>/miniconda3/envs/3.10.0/lib/python3.10/site-packages/PyQt6/Qt6/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt6/Qt6/lib/QtWidgets.framework/Versions/Current', 'A', 'SYMLINK')],
 'libpython3.10.dylib',
 False,
 False,
 False,
 [],
 'arm64',
 None,
 None)
