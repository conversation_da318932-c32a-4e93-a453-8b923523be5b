
import json, requests
def main(query: str, use_kg: str="否") -> dict:
    use_kg = True if use_kg == "是" else False
    response = requests.post(
        url="http://10.15.1.75/api/v1/dify/retrieval", 
        json={"query": query,"knowledge_id": "48c981f4240211f097440242ac1e0306","use_kg": use_kg,"retrieval_setting": {"top_k": 5}},
        headers={"Authorization": "Bearer ragflow-Y3M2YzOTZlMWZlNjExZjBhOWUyMDI0Mm"}
    )
    response.raise_for_status()
    result = "\n-----\n".join([item["content"] for item in response.json()["records"]])
    return {"result": result}


print(main("D07需要接哪些线"))