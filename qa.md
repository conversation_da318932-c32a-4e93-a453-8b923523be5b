## 无轨迹，无轨迹分段，轨迹分段异常情况的排查思路
**1. 检查平台数据处理逻辑与补报情况**

* **补报超期问题**：确认设备补报数据的时间是否超过平台允许的轨迹生成/分段时间上限（例如3天，当天算一天）。超期补报的数据可能无法生成轨迹或分段。
* **数据过滤规则**：确认平台是否对特定设备型号或场景配置了过滤规则，例如过滤卫星数低、速度为0的点，这些规则可能导致有效轨迹点不足以形成分段。

**2. 理解平台的轨迹分段逻辑**

* **点位差距大**：如果上一分段结束点和下一分段开始点位置差距较大，平台为了保证里程统计完整，可能不会进行分段。
* **卫星数低/速度0**：平台在轨迹分段时，会过滤掉卫星数过低或速度为0的轨迹点。如果一段轨迹点被过滤后会导致里程丢失，平台可能选择不分段以保证里程尽量完整。
* **停车时长**：平台或设备对停车时长的判定逻辑（例如最短5分钟，最长15分钟分段）可能影响分段。行驶中熄火停车几分钟后分段属于正常逻辑，但如果停车时长不满足分段条件则不会分段。
* **GPS点稀疏/速度0**：设备上报频率低或长时间速度为0的点，可能不满足分段条件（例如5分钟未驶出50米）。
* **隧道场景**：隧道内无GPS信号，平台通过直线连接计算里程，这本身不是分段问题，但可能影响对轨迹完整性的判断。

**3. 检查数据本身的完整性与时效性**

* **补报超期**：再次确认设备补报数据是否在规定天数内（例如3天，当天算一天）才能生成分段轨迹。
* **原始报文问题**：
    * 检查报文卫星数是否过低（例如低于8颗），导致被过滤无法分段。
    * 检查是否因点火/熄火报文丢失导致无法分段，这通常需要后台排查。如果确认是点熄火报文丢失，可能需要后台手动添加报文，重新触发计算。
* **MBS同步问题**：设备入库时信息同步到MBS失败，可能导致分段轨迹查询失败或无分段。这种情况下，可能需要后台手动添加点熄火报文，重新触发计算。

**4. 特定场景下的问题**

* **车辆轨迹正常但无分段轨迹（多种原因综合）**：
    * 再次检查是否超过补报时限（3天）。
    * 再次检查报文卫星数是否过低（如<8），导致被过滤。
    * 再次检查是否因点火/熄火报文丢失（需后台排查，可能需手动添加报文触发计算）。
    * 如果属于补报超期或数据问题（如点火报文丢失或MBS同步问题），可能需要后台修复数据（提工单）。

**总结排查步骤：**

* 确认数据时效性：是否为超期补报的数据。
* 检查数据质量：GPS卫星数是否过低，速度是否长时间为0。
* 核对报文完整性：是否存在点火、熄火报文丢失的情况（可能需要后台协助）。
* 理解平台分段规则：是否存在点位跳变过大、停车时长不足等不满足平台分段逻辑的情况。
* 检查MBS同步状态：确认设备信息是否成功同步（可能需要后台协助）。
* 联系后台支持：如果以上自查无法解决，可能涉及平台深层逻辑或数据问题，需要提交工单由后台技术人员进一步排查和修复。

## 设备出现无定位，定位漂移的问题排查
**1. 检查平台数据处理逻辑与补报情况**

* **补报超期问题**：确认设备补报数据的时间是否超过平台允许的轨迹生成/分段时间上限（例如3天，当天算一天）。超期补报的数据可能无法生成轨迹或分段。
* **数据过滤规则**：确认平台是否对特定设备型号或场景配置了过滤规则，例如过滤卫星数低、速度为0的点，这些规则可能导致有效轨迹点不足以形成分段。

**2. 理解平台的轨迹分段逻辑**

* **点位差距大**：如果上一分段结束点和下一分段开始点位置差距较大，平台为了保证里程统计完整，可能不会进行分段。
* **卫星数低/速度0**：平台在轨迹分段时，会过滤掉卫星数过低或速度为0的轨迹点。如果一段轨迹点被过滤后会导致里程丢失，平台可能选择不分段以保证里程尽量完整。
* **停车时长**：平台或设备对停车时长的判定逻辑（例如最短5分钟，最长15分钟分段）可能影响分段。行驶中熄火停车几分钟后分段属于正常逻辑，但如果停车时长不满足分段条件则不会分段。
* **GPS点稀疏/速度0**：设备上报频率低或长时间速度为0的点，可能不满足分段条件（例如5分钟未驶出50米）。
* **隧道场景**：隧道内无GPS信号，平台通过直线连接计算里程，这本身不是分段问题，但可能影响对轨迹完整性的判断。

**3. 检查数据本身的完整性与时效性**

* **补报超期**：再次确认设备补报数据是否在规定天数内（例如3天，当天算一天）才能生成分段轨迹。
* **原始报文问题**：
    * 检查报文卫星数是否过低（例如低于8颗），导致被过滤无法分段。
    * 检查是否因点火/熄火报文丢失导致无法分段，这通常需要后台排查。如果确认是点熄火报文丢失，可能需要后台手动添加报文，重新触发计算。
* **MBS同步问题**：设备入库时信息同步到MBS失败，可能导致分段轨迹查询失败或无分段。这种情况下，可能需要后台手动添加点熄火报文，重新触发计算。

**4. 特定场景下的问题**

* **车辆轨迹正常但无分段轨迹（多种原因综合）**：
    * 再次检查是否超过补报时限（3天）。
    * 再次检查报文卫星数是否过低（如<8），导致被过滤。
    * 再次检查是否因点火/熄火报文丢失（需后台排查，可能需手动添加报文触发计算）。
    * 如果属于补报超期或数据问题（如点火报文丢失或MBS同步问题），可能需要后台修复数据（提工单）。

**总结排查步骤：**

* 确认数据时效性：是否为超期补报的数据。
* 检查数据质量：GPS卫星数是否过低，速度是否长时间为0。
* 核对报文完整性：是否存在点火、熄火报文丢失的情况（可能需要后台协助）。
* 理解平台分段规则：是否存在点位跳变过大、停车时长不足等不满足平台分段逻辑的情况。
* 检查MBS同步状态：确认设备信息是否成功同步（可能需要后台协助）。
* 联系后台支持：如果以上自查无法解决，可能涉及平台深层逻辑或数据问题，需要提交工单由后台技术人员进一步排查和修复。

## 设备出现卫星数异常、数据延迟的排查思路

###   设备出现卫星数异常的排查思路：

1.  **确认报文内容**：检查报文内容是否包含定位信息，特别关注卫星数这一字段。如果报文中无卫星颗数显示，需要确认卫星数是设备上报的数据，并解释可能原因是设备在特定场景中获取不到定位信号。
2.  **检查设备环境**：确认设备安装环境是否影响GPS信号接收，例如地下车库等环境会遮挡GPS信号。
3.  **检查数据过滤规则**：确认平台是否对特定设备型号或场景配置了过滤规则（如过滤卫星数低的点），以及这些规则是否符合预期。
4.  **检查设备定位漂移**：如果定位漂移严重（如跨省），检查漂移点GPS搜星数量，如果星数正常（如>10），平台默认不过滤。将问题和数据反馈给终端厂商排查设备问题。

###   设备出现数据延迟的排查思路：

1.  **检查设备在线状态**：确认设备在平台上是否显示在线，如果离线，先排查设备上线问题。
2.  **检查原始报文**：在后台系统查看设备是否有报文上传（注册、鉴权、定位报文）。
3.  **检查设备配置**：确认设备配置是否正确（如上传间隔、IP/域名、端口）。
4.  **检查SIM卡状态**：确认设备使用的SIM卡是否正常（如是否欠费、是否为定向卡且白名单配置正确）。
5.  **检查平台处理逻辑**：如果原始报文正常但平台显示状态更新延迟，排查平台的数据处理、缓存或定时任务逻辑。
6.  **查看设备GPS报文上报情况**：确认是否存在延迟。
7.  **查看平台对设备注册报文的响应日志**：确认平台是否成功响应。

##  设备状态异常排查思路（平台显示设备离线、在线状态与设备不相符）

当出现设备状态异常，即平台显示的状态与设备的实际状态不符时，可以按照以下思路进行排查：

**1. 检查设备在线状态：**

* 确认设备在平台上是否显示在线。如果离线，先排查设备上线问题。

**2. 检查原始报文：**

* 在后台系统（如 oneway、DCS、ELK）查看设备是否有报文上传（注册、鉴权、定位报文）。
* 确认报文内容是否包含 ACC 状态等关键字段。

**3. 检查平台处理逻辑：**

* 如果原始报文正常但平台显示异常（如状态更新延迟），排查平台的数据处理、缓存或定时任务逻辑。

**4. 特定原因排查：**

* **设备状态显示异常（在线静止但实际离线/运动）：**
    * 解释可能原因：DMS 状态更新失败或延迟（如因网络波动、服务重启、边界条件处理问题、设备断连重连、设备先上线后绑定未触发更新）。
    * 临时方案：重启设备可恢复；或提工单后台手动刷新状态。
* **设备一直在线静止（D32/D07，ACC 接长电）：**
    * 解释：平台使用 ACC 状态判断运动（部标设备）；若 ACC 接长电则一直显示运动。
    * 解决方案：优化平台状态判断逻辑（结合速度等）；建议客户正确接线。
* **设备离线但平台显示在线静止（D32）：**
    * 解释：设备持续上报心跳维持连接，但无有效 GPS 或 ACC 状态变化，平台状态更新逻辑待优化。
    * 解决方案：等待平台状态判断逻辑优化。

**5. 其他：**

* 重启设备：尝试对设备进行断电重启，观察数据上报是否恢复正常。
* 联系终端厂家：如果原始报文在设备端就异常或设备频繁死机/重启，问题可能在设备硬件或固件，需联系终端厂家排查。
* 提交工单/提需求：如果是平台处理逻辑问题或需要优化现有功能，提交工单或提需求给产品部门。

##   设备注册失败，无法连接平台的排查思路

1.  **检查设备是否连接了正确的设备接入地址和端口。**
2.  **检查物联卡白名单是否配置正确（需包含平台相关IP）。**
3.  **检查设备上报的报文是否通过了鉴权。**
4.  **检查设备物联卡是否有效，是否存在停机的情况。**


## 设备绑定及白名单问题:当设备无法导入白名单或无法绑定车辆时，可以按照以下步骤排查

1. 核对设备信息：仔细核对设备的SN和IMEI是否与导入/绑定时使用的信息完全一致。
2. 检查白名单：确认设备的SN或IMEI是否已正确添加到平台的白名单中。
    * **提示手机号已存在**：检查该通讯号（或IMEI后11位）是否已被其他设备在白名单中占用。如是，需先删除冲突的记录。
3. 检查设备归属：确认设备是否已归属到正确的平台或企业（如是否需要从Oneway划拨到车队平台）。
4. 检查设备绑定状态：确认设备是否已经绑定到其他车辆或账号。
5. 检查导入文件格式：如果是批量导入白名单，检查导入文件的格式和字段是否符合要求。
6. 检查设备类型：确认导入/绑定时选择的设备类型是否与实际设备类型匹配。
7. 联系平台技术：如果信息核对无误但仍无法导入或绑定，可能需要后台技术协助排查平台数据或绑定逻辑问题。
8. **设备绑定异常（个人路尚设备无法绑定车队）**：后台处理将设备划拨到车队平台。
9. **设备绑定异常（提示已注册但未绑定）**：后台恢复数据。
10. **设备绑定异常（IMEI未在白名单）**：平台操作将IMEI加入白名单。
11. **设备绑定异常（行车卫士设备）**：检查设备是否被其他应用（如个人路尚）绑定，需先解绑或后台处理。
12. **设备白名单导入SN错误**：提供正确的IMEI与SN对应关系，提数据处理审批工单，由后台更新。
13. **删除白名单设备失败（已绑定）**：需先在平台解绑车辆，再删除白名单。

##   设备视频出现播放卡顿、连接失败的排查思路


1.  **检查设备在线状态**：确认设备是否在线且视频通道已配置。
2.  **检查网络连接**：确认设备和平台之间的网络连接是否稳定，带宽是否足够。
3.  **检查SIM卡**：如果使用定向卡，确认白名单配置是否正确（需包含平台IP及 **FTP 地址 IP: ****************）。
4.  **检查双平台连接**：如果设备同时连接多个平台（如星冠），尝试断开其他平台，只连接当前平台测试，确认主链路设置。
5.  **检查视频码流**：确认平台默认播放的码流设置（主码流/子码流）是否合适，尝试切换码流。
6.  **检查平台视频服务**：确认平台视频服务（如 RTVS）运行是否正常。
7.  **检查设备端问题**：排查设备自身的上传速度、固件问题或提前上报完成等异常行为。
8.  **联系终端厂家**：如果问题确定在设备端，需联系终端厂家排查。

##   设备历史视频上传、下载异常的排查思路

1.  **检查设备在线状态**：确认设备是否在线且视频通道已配置。
2.  **检查网络连接**：确认设备和平台之间的网络连接是否稳定，带宽是否足够。
3.  **检查SIM卡**：如果使用定向卡，确认白名单配置是否正确（需包含平台IP及 **FTP 地址 IP: ****************）。
4.  **检查设备端问题**：排查设备自身的上传速度、固件问题或提前上报完成等异常行为。
5.  **联系终端厂家**：如果问题确定在设备端，需联系终端厂家排查。
6.  **历史视频上传失败（定向卡）**：
    * 确认 FTP 地址（**************）是否已加入物联卡白名单。
    * 指导客户联系运营商添加白名单。

## 账号与权限问题: 当账号无法登录、权限异常或账号信息错误时，可以按照以下步骤排查

1. 核对账号信息：仔细核对登录账号和密码是否正确。
2. 检查账号状态：在后台管理界面查询账号状态（是否激活、是否过期、是否被锁定/屏蔽）。
    * **账号长期未使用无数据**：解释原因是被平台自动屏蔽。解决方案：后台重新开启数据权限。
    * **账号锁定（多次密码错误）**：解释理论上24小时或凌晨自动解锁。如紧急，提供账号由运维后台解锁。
3. 检查认证系统：如果使用第三方认证（如4A），检查认证系统状态和账号授权情况。
4. 查询操作日志：查询账号相关的操作日志，确认是否有被修改、删除或权限变更的记录。
    * **账户手机号与短信接收不一致**：查询日志确认被其他管理员修改。
    * **账号角色异常（如总账户变用车人）**：查询日志确认被其他企业管理员修改。
5. 检查角色和权限配置：查看账号被分配的角色以及这些角色对应的具体权限。
    * **部门管理员无费用登记导出/导入/删除权限**：解释默认权限设置。解决方案：提工单由后台为特定企业或角色添加相应权限编码。
    * **调度员/审批员权限**：解释各自主要职责（审批、派车），调度员可看监控，审批员不能。提供详细权限映射表供参考。
6. 检查账号关联：如果是账号创建问题，检查是否存在手机号重复等系统校验问题。
    * **手机号验证码登录提示错误（手机号重复）**：解释原因：该手机号在系统中存在多个账号（可能是历史原因或测试账号）。解决方案：后台清理重复或无效的账号。
    * **忘记密码手机号重复**：解释因手机号关联多个账号无法通过手机号重置。解决方案：1. 使用账号密码登录页的“忘记密码”（需输入账号）。 2. 联系企业管理员重置密码。 3. 后台刷库（需工单）。
7. 检查流程占用：如果账号无法删除，检查是否参与了未结束的流程（包括用车工单、维修申请、**创建的流程**等）。
    * **账号无法删除（维修流程未结束）**：需先结束相关流程。
    * **账号无法删除（创建的流程未删除）**：需先删除该用户创建的流程。
8. 联系平台管理员/技术：如果是权限配置错误、账号状态异常或需要后台操作（如重新授权、恢复账号），联系平台管理员或技术人员处理。
9. 提需求/优化：如果是账号创建校验逻辑问题或权限继承机制不合理，可以提需求优化。
10. **账号无法删除（人车匹配）**：
    * 解释原因：账号仍与车辆存在人车匹配关系。
    * 解决方案：后台解除人车匹配关系。
11. **司机修改密码失败**：
    * 解释原因：权限配置错误（前期安全检查误操作）。
    * 解决方案：后台恢复司机修改密码权限。
12. **账号无法调整为企业管理员（提示不能修改为一级部门）**：
    * 解释规则：平台限制普通账号直接升级为企业管理员。
    * 解决方案：删除原账号，重新添加为企业管理员。
13. **账号无法切换角色**：
    * 解释：平台不支持登录后在应用内切换角色，需退出重新登录选择。
14. **OMP查询客户账户信息无权限**：
    * 检查操作账号的4A金库策略配置是否正确或过期。
    * 联系4A管理员重新授权或修改策略（需提供最新金库编码）。
15. **账号禁用需求**：
    * 解释：平台无“禁用”状态。
    * 解决方案：需客户提供函件，提工单由后台删除账号；结束后再重新添加。
16. **新增账号用户名规则**：
    * 解释：司机账号用户名无严格格式限制（可用手机号）；非司机账号需字母数字组合。批量导入遵循较严规则。

## App兼容性及安装问题: 当手机App出现闪退、功能异常或无法安装时，可以按照以下步骤排查

1. 获取设备信息：记录用户手机型号、操作系统版本和App版本。
2. 检查兼容性列表：查看App是否支持该操作系统版本，是否存在已知的兼容性问题。
    * **纯血鸿蒙系统无法使用**：解释APP暂不支持纯血鸿蒙。解决方案：等待小程序版本上线。
3. 基础故障排除：建议用户强制关闭App、清除App缓存、卸载并重新安装App。
    * **升级后闪退/无法使用**：主要解决方案是卸载后通过二维码或链接重新安装最新版本。
    * **升级弹窗点确定无反应/闪退**：解释是特定版本（4.7.0）打包问题导致无法自动拉起升级。解决方案：点击弹窗的“取消”按钮进入旧版APP，或卸载后扫码重装最新版。
4. 检查安装源：如果是安装问题，确认App安装包来源是否官方，尝试使用提供的兼容包（针对旧系统）。
    * **扫码无法下载（荣耀手机）**：建议使用手机自带浏览器扫描二维码或直接复制下载链接到浏览器打开。
5. 检查系统设置：提示用户检查手机系统设置，如是否需要信任企业级App（iOS）、是否开启了纯净模式/增强防护（部分安卓）。
6. 测试复现：在相同或相似的手机型号和系统版本上测试是否能复现问题。
7. 联系开发团队：如果问题无法解决且疑似兼容性问题，将详细信息反馈给开发团队排查和修复。
8. **APP安装提示风险/报毒**：
    * 解释原因：手机厂商安全校验，非应用市场上架APP会有此提示。
    * 说明APP已在工信部备案。
    * 指导用户：点击“继续安装”或在手机设置中暂时关闭风险检测/纯净模式。
9. **APP视频客服入口消失**：
    * 解释原因：安全合规要求，在线客服功能升级后取消。
10. **APP日常车务拍照闪退（安卓）**：
    * 解释原因：隐私合规整改引入的权限问题。
    * 临时方案：回退到旧版本（提供二维码或链接）。
    * 解决方案：等待新版本修复上线。
11. **APP推送通知红点/角标不显示**：
    * 解释原因：与手机系统设置（通知权限、角标显示权限）及APP后台存活状态有关。
    * 指导用户：检查手机系统和APP的通知、角标设置。
12. **APP补单闪退/报错（选择未来时间）**：
    * 解释原因：Bug或兼容性问题。
    * 解决方案/Workaround：指导用户不要选择超过今天的时间；或使用Web端补单。
13. **APP无法保存密码**：
    * 解释：APP本身无此功能，部分手机系统可能支持。
    * 建议：使用短信验证码登录。
14. **APP网络异常/无法连接（特定运营商/地区）**：
    * 尝试切换网络（WiFi/其他运营商流量）测试。
    * 如确认是特定运营商网络问题，需反馈给平台网络负责人协调运营商排查。

## 第三方集成及API调用问题: 当平台与第三方系统集成或API调用出现问题时，可以按照以下步骤排查

1. 确认对接方式：确认是数据抓取还是数据推送，是API调用还是其他方式。
2. 检查接口状态：确认第三方系统提供的接口是否正常运行。
3. 检查网络连接：确认平台与第三方系统之间的网络连接是否畅通，IP/域名白名单是否配置正确。
4. 检查接口参数：确认调用API时使用的参数是否正确，包括devkey、请求body、时间范围等。
5. 检查数据格式：确认导入/导出的文件格式或API传输的数据格式是否符合要求。
6. 查看日志：查看平台调用第三方接口的日志或第三方系统接收/返回数据的日志，查找错误信息。
    * **TSSP数据推送失败**：查看推送日志，确认对端是否返回错误（如地址配置错误、网络不通、对端服务异常）。
    * **数据推送到应急平台失败**：检查TSSP推送日志，确认推送地址是否正确，对端是否返回错误。如此例中对端返回`{"result":1,"resultNote":null}`表示失败，需对方排查。
7. 联系第三方系统人员：如果问题出在第三方系统端（如接口未返回数据、配置变更导致异常、接收失败），需联系对方系统人员排查。
8. 联系平台技术：如果问题出在平台调用逻辑或数据处理上，联系平台技术排查。
9. **联合安业钥匙配置**：解释测试/现网用同车牌即可；新增需在联合安业后台配置。
10. **高德/用车制度配置错误**：指导客户联系高德后台删除多余配置。
11. **高德/配置变更导致异常**：指导客户联系高德后台调整配置。
12. **OA数据同步失败（接口未返回）**：需对方系统人员排查。
13. **OA数据同步失败（数据未生成）**：需对方等待数据生成。
14. **API接口调用失败**：要求客户提供body和报错信息，检查devkey和参数。
15. **设备推送配置冲突（TSSP）**：解释一个设备在TSSP只能配置一个推送应用，系统会随机推送。需用户确认保留哪个推送配置，提工单删除多余的。
16. **平台传输协议查询**：告知用户平台主要使用JT/T 808协议。
17. **809协议转发接入需求**：解释平台目前只支持808协议推送数据 *到* 平台（jt808.cmobd.com:6010），不支持作为809下级平台接收数据。

## 流程与工单问题: 当平台内的工单流程执行异常、状态错误或操作受限时，可以按照以下步骤排查

1. 检查工单状态：确认工单当前的具体状态（如执行中、待审批、已拒绝）。
2. 检查流程配置：查看该类型工单的流程配置，包括审批节点、审批人、条件分支、权限要求等。
    * **网约车周末用车审批异常**：解释原因（申请人未选二级审批人）。解决方案：下版本优化为必选。
    * **默认流程审批员角色**：解释必须是审批员角色账号，否则无法创建。
    * **审批流程无对应部门管理员**：解释流程会向上级查找或根据配置查找，导致非本部门管理员收到短信/待办。
3. 检查相关实体状态：确认工单关联的车辆、司机、申请人等实体状态是否正常，是否被其他流程占用。
    * **历史工单未结束占用车辆**：需先结束历史工单。
4. 检查操作权限：确认当前操作人是否具有执行该步骤的权限（如调度员是否有权限变更特定工单的司机）。
    * **调度员变更司机失败**：解释权限不足（如只能指派本部门及下级）。
    * **管理员无法处理申诉**：检查是否登录了正确的账号（如部门管理员 vs 企业管理员），以及工单是否属于其管辖范围。
    * **审批时选不到下一级审批人 (Web端)**：解释是安全整改收紧权限。Workaround: 使用APP审批。Fix: 等待后续恢复。
5. 检查历史记录：查看工单的历史流程记录，确认之前的操作是否正确或是否存在异常。
    * **工单司机姓名显示错误**：需进一步排查数据。
6. 解释流程逻辑：向用户解释平台的流程逻辑（如工单时间不能重合、特定角色权限范围、拆单功能）。
    * **工单中止流程**：解释中止条件、效果及后续操作。
    * **拆单逻辑**：解释拆单是将一个申请拆成多个独立工单。
    * **维修申请审批逻辑**：解释可指定企业内任一管理员，最多8次，可指定自己。
    * **补单时间逻辑**：解释补单的实际时间取计划时间。
    * **维修费用填写逻辑**：解释由申请人在申请通过后，在“我的车务申请”页面填写。
    * **工单里程计算逻辑**：解释按实际开始到结束时间内的分段轨迹里程计算，工单完结后计算。
    * **工单里程计算偏差（分段里程 vs 工单里程）**：解释计算逻辑差异（工单里程按完整分段累加，即使起止时间在分段内）。
    * **工单申诉流程**：解释司机/用车人申诉及管理员处理流程，确认操作入口。用车人同意申诉后，管理员可在已完成派单编辑申诉里程。司机申诉后，用车人仍可申诉。
    * **工单调度分配（拆单后）**：解释拆单后，如调度时选车失败（如车辆已被占用），可能导致后续工单状态异常或只分配给部分调度员。
7. 联系平台技术：如果流程配置或执行出现系统问题，需联系平台技术排查。
8. 提工单处理：如果是数据错误导致流程异常（如历史工单未结束、状态错误），可能需要提工单由后台处理。
    * **工单状态异常（故障期间导致）**：提工单后台修复（删除工单或清理待办）。
    * **工单状态异常（已拒绝但待审批）**：提工单后台修复。
    * **工单状态异常（已完成但待审批）**：提工单后台修复。
    * **工单状态异常（审批后仍待审批/司机无任务）**：提工单后台修复。
    * **工单状态异常（已完成但待办列表仍显示）**：提工单后台清理待办。
9. **延迟回单提醒逻辑**：解释执行中的派单超计划结束2小时发短信提醒司机。
10. **工单里程计算逻辑**：解释按实际开始到结束时间内的分段轨迹里程计算，工单完结后计算。
11. **工单里程计算偏差（分段里程 vs 工单里程）**：解释计算逻辑差异（工单里程按完整分段累加，即使起止时间在分段内）。
12. **工单申诉流程**：解释司机/用车人申诉及管理员处理流程，确认操作入口。用车人同意申诉后，管理员可在已完成派单编辑申诉里程。司机申诉后，用车人仍可申诉。
13. **工单导出申诉事由缺失**：解释当前只导出用车人申诉事由。建议：提需求优化。
14. **批量处理异常用车告警状态**：提工单，提供类型、时间范围、部门、处理人信息，后台SQL处理。
15. **审批车务申请提示管理员信息错误 (Web端)**：解释是安全整改收紧权限。Workaround: 使用APP审批。Fix: 等待后续恢复。
16. **拆单后工单状态异常/无法派车**：解释是拆单时选车失败或状态扭转错误。Solution: 后台修复数据。Restriction: 重选审批人后的单子暂不支持拆单。
17. **拆单后司机看不到任务**：解释是数据未补充完整。Solution: 后台处理数据。
18. **批量完结历史工单**：解释可通过后台SQL将指定时间前的工单状态刷为“已完成”，但不保证操作记录和流程图完整。需提工单处理。
19. **维修申请实际金额填写**：解释由申请人在审批通过后，在“我的车务申请”页面填写。
20. **工单状态异常（已拒绝但司机端待出车）**：解释是历史数据问题。解决方案：后台修复数据（提工单）。
21. **批量调整用户部门**：解释平台无此功能。需客户提供函件+明细，提工单后台处理。
22. **删除用户数据需函件审批**：解释为符合安全审计要求，删除用户数据需提供客户盖章函件，并通过审批工单流程处理。
23. **工单流程名称显示不一致**：查询后台日志，确认是否因流程被编辑或删除导致（编辑/删除后，旧流程ID失效，历史工单仍显示旧名称，但流程管理中不可见）。

## OMP常见问题及排查思路

* **OMP重置密码提示未配置策略**：
    * 联系平台技术检查金库策略配置。
* **OMP批量导入车辆部门编号获取**：
    * 指导用户登录企业端（zqcd）在组织部门页面查看部门编号。
* **OMP导出功能需金库审批**：
    * 通知相关人员申请金库权限。
* **OMP无法查询客户账户信息（权限问题）**：
    * 解释：4A账号金库策略问题。
    * 解决方案：联系4A管理员检查/更新权限（需提供正确金库编码）。
* **OMP查询客户账户信息无权限**：
    * 检查操作账号的4A金库策略配置是否正确或过期。
    * 联系4A管理员重新授权或修改策略（需提供最新金库编码）。
* **OMP批量导入车辆提示企业不能为空**：
    * 检查模板是否正确，确认“企业名称”列已填写。
    * 如果使用新模板，确认“企业编号”列已填写（需登录企业端查询编号）。

