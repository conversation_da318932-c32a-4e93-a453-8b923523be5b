import os
import re
import json
from collections import defaultdict

def split_by_day(input_text):
    """按天切割消息内容"""
    # 正则表达式匹配日期格式 [YYYY-MM-DD HH:MM:SS]
    date_pattern = r'^\[(\d{4}-\d{2}-\d{2}) \d{2}:\d{2}:\d{2}\]'
    
    # 用于存储按天分组的消息
    messages_by_day = defaultdict(list)
    
    # 按行处理输入文本
    lines = input_text.strip().split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i]
        # 检查行是否开始一条新消息（以日期开头）
        match = re.match(date_pattern, line)
        
        if match:
            current_date = match.group(1)
            current_message = line
            
            # 向后查找直到找到下一条消息的开始
            j = i + 1
            while j < len(lines):
                next_line = lines[j]
                # 如果下一行以日期开头，则是新消息
                if re.match(date_pattern, next_line):
                    break
                # 否则，将该行添加到当前消息
                current_message += "\n" + next_line
                j += 1
            
            # 将完整消息添加到对应日期
            messages_by_day[current_date].append(current_message)
            i = j  # 跳到下一条消息
        else:
            # 如果不是以日期开头，跳过这一行（通常不应该出现）
            i += 1
    
    return messages_by_day

def save_messages_by_day(messages_by_day, output_dir="output"):
    """将按天分组的消息保存到单独的文件中"""
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 为每一天创建一个文件
    for date, messages in messages_by_day.items():
        file_name = f"{date}.txt"
        file_path = os.path.join(output_dir, file_name)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            for message in messages:
                f.write(f"{message}\n\n")
        
        print(f"已保存 {date} 的消息，共 {len(messages)} 条")

def main():
    print("按天切割消息工具")
    print("=" * 50)
    
    input_choice = input("请选择输入方式：\n1. 从文件读取\n2. 手动输入\n请输入选择（1或2）：")
    
    if input_choice == "1":
        file_path = input("请输入消息文件路径：").strip('"')
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                input_text = f.read()
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试其他编码
            with open(file_path, 'r', encoding='gbk') as f:
                input_text = f.read()
    else:
        print("请输入消息内容，输入完成后请输入 'END' 单独一行表示结束：")
        lines = []
        while True:
            line = input()
            if line == 'END':
                break
            lines.append(line)
        input_text = "\n".join(lines)
    
    # 按天切割消息
    messages_by_day = split_by_day(input_text)
    
    # 保存结果
    output_dir = input("请输入输出目录名称（默认为'output'）：") or "output"
    save_messages_by_day(messages_by_day, output_dir)
    
    print(f"处理完成！输出文件保存在 {os.path.abspath(output_dir)} 目录")

if __name__ == "__main__":
    main() 