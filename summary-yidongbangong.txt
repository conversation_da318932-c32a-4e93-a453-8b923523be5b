1111111111问题1:用户需求电子栅栏设置时间改为日期方式
时间:2024年1月2日
排查过程：用户提出需求，希望将电子栅栏设置时间从0-24点改为日期方式（如1号-5号）。
排查结果：产品团队接受需求，后续将排期整改。


1111111111问题2:行车卫士政企车队平台派单数据保存时长
时间:2024年1月3日
排查过程：用户询问派单数据保存时长，是否为永久保存。
排查结果：派单数据保存3年，轨迹数据保存1年。


1111111111问题3:监控平台到期保险、年检提醒时间调整
时间:2024年1月4日
排查过程：用户希望将到期提醒时间从提前3个月调整为提前1个月。
排查结果：提醒时间默认提前90天，数据库写死，需通过脚本修改。


1111111111问题4:用户删除车辆后轨迹记录丢失
时间:2024年1月9日
排查过程：用户删除车辆后，12月份的轨迹记录丢失，希望找回。
排查结果：通过设备号找回轨迹记录，提供Excel格式的轨迹点或轨迹段。


1111111111问题5:行车卫士政企车队平台设备绑定失败
时间:2024年1月9日
排查过程：用户绑定设备时提示设备不存在，白名单查询正常。
排查结果：设备未在oneway平台划拨，需先划拨设备再进行绑定。


1111111111问题6:用户反馈车辆轨迹异常
时间:2024年1月23日
排查过程：用户反馈设备在星冠上线正常，但在政企车队平台离线，重启后恢复正常。
排查结果：设备GPS报文上报延迟，9点以后的报文9点50才上报到平台。


1111111111问题7:用户反馈短信发送失败
时间:2024年1月30日
排查过程：用户反馈用车人未收到短信提醒。
排查结果：执行中的派单超过计划结束时间两小时会发送短信提醒，其他情况不会发送。


1111111111问题8:用户反馈系统登录异常
时间:2024年2月27日
排查过程：用户反馈登录系统异常，派车系统无法正常派车。
排查结果：系统升级导致部分功能异常，需等待升级完成后恢复正常。


1111111111问题9:用户反馈APP闪退
时间:2024年3月19日
排查过程：用户反馈APP打开用车管理时闪退。
排查结果：APP版本升级导致，需强制升级到最新版本。


1111111111问题10:用户反馈派单状态异常
时间:2024年3月18日
排查过程：用户反馈派单已完成，但仍显示在审批中。
排查结果：审批节点存在脏数据，需通过工单流程修复。


1111111111问题11:用户反馈未匹配用车告警失效
时间:2024年3月21日
排查过程：用户反馈未匹配用车告警失效，车辆使用未产生告警。
排查结果：未匹配用车开关关闭，需打开开关才能产生告警。


1111111111问题12:用户反馈派单数据提取问题
时间:2024年3月21日
排查过程：用户需要提取21-22年的派单数据。
排查结果：需单独恢复数据，用户账号jlxjcy@fjsjcy已恢复21-22年派单数据。


1111111111问题13:用户反馈工程维护场景派单问题
时间:2024年3月25日
排查过程：用户反馈工程维护场景少于3人无法派单。
排查结果：工程维护场景不需要三人以下用车场景，可以正常派单。


1111111111问题14:用户反馈里程预估功能异常
时间:2024年3月27日
排查过程：用户反馈里程预估功能无法预估里程。
排查结果：需选择期待车型后才能预估里程。

1111111111问题15: D31型号设备轨迹漂移问题
时间: 2024年4月1日
设备号: 25022206001394、25022206001206、25022206001454、25022206001401、25022206001674
排查过程：用户反馈里程异常，平台查询设备存在轨迹漂移现象，部分设备一个月内出现多次漂移。
排查结果：设备未接入轨迹过滤服务，导致轨迹漂移现象。平台侧后续可以针对设备型号设置过滤规则，优化方案需进一步评估。


1111111111问题16: 设备绑定异常问题
时间: 2024年4月3日
设备号: 70007117519（车牌号：陕U7113Y）
排查过程：用户反馈绑定D32设备的车辆无法打开界面。
排查结果：设备绑定关系异常，需进一步排查设备归属和绑定状态。


1111111111问题17: 调度员账户登录查不到派单问题
时间: 2024年4月6日
账户: rx1103@rxjs
排查过程：用户使用调度员账户登录查不到4月3号之后的派单，无法正常派单生产。
排查结果：调度员账号状态正常，用户可能登录了错误的账号（管理员角色而非调度员角色），建议核实账号权限。


1111111111问题18: 车辆管理系统登录问题
时间: 2024年4月12日
手机号: 15923082101
排查过程：用户反馈车辆管理系统登录时提示手机号被占用，无法获取验证码。
排查结果：该手机号对应多个账号，用户需通过账号密码登录并重置密码。


1111111111问题19: 设备绑定异常问题
时间: 2024年4月8日
设备号: 3613230013264213、861196042642136、3613230013248339、861196042483390
排查过程：用户反馈设备绑定关系异常，提示绑定失败。
排查结果：设备归属行车卫士平台，需通过行车卫士平台查看绑定状态。


1111111111问题20: 围栏告警偏差问题
时间: 2024年4月10日
车牌号: 辽HBYQGJ
设备号: 3613310110004943
排查过程：用户反馈进出围栏的告警位置与实际位置有偏差。
排查结果：栅栏在地图上的渲染位置不一致，导致告警位置偏差。平台侧已修复栅栏纠偏问题。


1111111111问题21: 设备离线问题
时间: 2024年4月12日
设备号: 018940742225、018960351629、018960351202
排查过程：用户反馈设备显示离线，但设备有报文上传。
排查结果：平台维护导致设备最后定位时间异常，设备再次上线后恢复正常。


1111111111问题22: 工单状态异常问题
时间: 2024年4月15日
工单号: 2404100685、2404110473
排查过程：用户反馈工单状态卡在待审批，司机无法结束用车。
排查结果：工单状态异常，平台升级后修复了部分工单状态问题，部分工单需手动处理。


1111111111问题23: 设备上线问题
时间: 2024年6月7日
设备号: 867700071372970、867700071417742
排查过程：用户反馈设备上报报文但平台未上线，切换到其他平台正常。
排查结果：设备未上报鉴权报文，平台未收到GPS数据，需排查白名单和设备上报问题。


1111111111问题24: 地图自动刷新问题
时间: 2024年6月6日
排查过程：用户反馈地图界面无法自动刷新，需手动刷新。
排查结果：平台升级后地图刷新逻辑调整为固定30秒刷新，部分用户可能因网络问题无法自动刷新。
1111111111问题25: 网约车功能测试异常
时间: 2024年6月11日 08:39
排查过程：售前测试网约车功能时弹出异常
排查结果：使用iotdemo测试账号无法使用，需在高德平台添加实际用车人信息，确保两个系统同步


1111111111问题26: 设备数据推送问题
时间: 2024年6月11日 09:36
设备号: 25012100013103
排查过程：设备数据推送至应急平台
排查结果：设备在tssp配置了两个应用，需删除其中一个，设备只能配置一个应用


1111111111问题27: 设备在线状态异常
时间: 2024年6月11日 16:51
设备号: 018940742337
排查过程：车辆在运动，APP显示有速度，但状态显示为静止
排查结果：设备在对应时间点上离线报文，设备和平台断连导致状态显示异常


1111111111问题28: 设备轨迹查询问题
时间: 2024年6月12日 08:42
排查过程：用户希望查看选中车辆的全部轨迹，而不是分段查看
排查结果：目前平台不支持查看全部轨迹，需按天分段查看


1111111111问题29: 车务通图片显示问题
时间: 2024年6月12日 09:59
排查过程：用户反馈车务通右侧地址显示车辆图片不明显
排查结果：优化需求已记录，将定期处理


1111111111问题30: 设备速度显示不一致
时间: 2024年6月12日 11:28
排查过程：行驶中的车辆在第一个界面有速度，第二个界面没有速度
排查结果：平台老逻辑遗留问题，第二个面板的速度获取不到，后续将优化


1111111111问题31: 设备历史轨迹查询问题
时间: 2024年6月19日 15:13
设备号: 018960352358
排查过程：设备补传了昨天的报文，但查询不到历史轨迹
排查结果：平台业务规则限制，只能查询绑定后的轨迹，补报的轨迹无法查询


1111111111问题32: 设备白名单添加问题
时间: 2024年6月21日 11:59
设备号: 25012100024565
排查过程：设备添加白名单时提示“终端手机号已存在”
排查结果：设备前期被加入冷链平台，需删除冷链白名单后重新添加


1111111111问题33: 车辆里程显示异常
时间: 2024年6月21日 09:04
排查过程：用户反馈车辆昨天收车公里数和今天不一样，相差几十公里
排查结果：平台上线了里程优化模块，短时间数据异常，后续已恢复正常


1111111111问题34: 维修申请逻辑问题
时间: 2024年6月20日 09:14
排查过程：用户询问维修申请的逻辑，管理员是否可以指定审批人及次数限制
排查结果：企业下所有管理员都可以指定，最多8次


1111111111问题35: 车辆编辑保存失败
时间: 2024年6月25日 09:15
排查过程：车辆编辑后无法保存，提示错误
排查结果：当天有安全检查，部分操作可能受影响，建议稍后重试


1111111111问题36: 轨迹查询与里程统计数据差异
时间: 2024年6月25日 15:11
排查过程：用户反馈轨迹查询和里程统计数据有差异
排查结果：轨迹分段时取小数点一位数，里程油耗统计是整体相加，存在微小差异


1111111111问题37: 工单操作记录查询
时间: 2024年6月25日 17:21
排查过程：用户反馈工单显示司机点击了开始和结束，但司机称未操作
排查结果：操作日志显示司机确实操作了开始和结束，平台记录为司机操作


1111111111问题38: 设备类型显示问题
时间: 2024年6月27日 13:58
设备号: 018960351604
排查过程：设备类型显示为纯定位终端，实际为OBD类型
排查结果：平台业务侧未存储设备类型，需刷成OBD类型以显示正确信息
1111111111问题39:重庆移动反馈车辆轨迹出现漂移导致里程增加
时间:2024年7月1日
排查过程：通过查询后台轨迹页面分析当天报文
排查结果：平台服务正常，设备网络异常无法与平台通信


1111111111问题40:重庆移动反馈审批员账号登录后没有审批按钮
时间:2024年7月3日
排查过程：检查账号权限和登录流程
排查结果：登录错误，需使用部门管理员账号审批


1111111111问题41:陕西移动反馈申请单子目的地无法选择定位地点
时间:2024年7月3日
排查过程：检查系统配置和地图选点功能
排查结果：地图选点失效，更新配置后恢复正常



1111111111问题42: 重庆移动用车场景分部门禁用功能需求
时间: 2024年9月11日 10:30
排查过程：用户询问重庆移动的用车场景是否可以实现分部门禁用功能。
排查结果：目前系统不支持该功能，只有企业管理员可以查看、新增、编辑和删除用车场景。


1111111111问题43: D31设备无法回传位置信息
时间: 2024年9月11日 16:49
设备号: D31
排查过程：客户按照表格添加DPI白名单后，D31设备只能回传注册报文，无法回传位置信息。
排查结果：客户经理重新提交策略并生效，但问题仍未解决。平台服务正常，设备网络异常无法与平台通信。


1111111111问题44: 网页版登录异常
时间: 2024年9月11日 20:32
排查过程：用户反馈网页版无法登录。
排查结果：系统正在升级服务，已恢复。


1111111111问题45: 账号锁定问题
时间: 2024年9月11日 21:23
排查过程：用户反馈账号因多次登录失败被锁定。
排查结果：账号锁定后24小时自动解锁，或通过运维手动解锁。


1111111111问题46: 重庆移动怠速告警过多
时间: 2024年9月12日 09:07
排查过程：重庆移动用户反馈系统新增了大量怠速告警，触发条件为ACC=1、速度=0持续5分钟，每小时不重复触发。
排查结果：怠速告警功能默认开启，重庆移动车辆数量较多，导致告警数量较大。建议关闭怠速告警推送。


1111111111问题47: 车辆状态显示异常
时间: 2024年9月13日 09:41
排查过程：用户反馈车辆在行驶中，平台显示为静止状态。
排查结果：平台服务异常，部分服务重启导致状态更新延迟，已恢复。


1111111111问题48: 未匹配用车告警时间不匹配
时间: 2024年9月12日 14:53
车牌号: 辽GL3514
设备号: 15131651184
排查过程：用户反馈平台告警时间与报文时间不匹配。
排查结果：平台告警时间为PAAS推送时间，设备报文时间为实际发生时间，存在时间差。


1111111111问题49: 设备在线静止状态异常
时间: 2024年9月27日 09:27
设备号: 70007117686
排查过程：设备最后回传报文时间为9月10日，但系统仍显示为在线静止状态。
排查结果：DMS状态更新失败，需手动恢复。


1111111111问题50: 设备轨迹缺失
时间: 2024年9月14日 08:58
设备号: 25012100018555、25012100018626
排查过程：用户反馈设备在9月13日没有轨迹数据。
排查结果：设备在9月13日晚上才绑定车辆，绑定后行驶才能查到轨迹分段。


1111111111问题51: 派车单数据缺失
时间: 2024年9月18日 14:09
排查过程：用户反馈2018年至2019年7月的派车单数据缺失。
排查结果：系统最早派车单数据为2019年4月2日，2018年无派车单数据。


1111111111问题52: 工单申诉事由导出不全
时间: 2024年9月18日 16:43
排查过程：用户反馈部分工单导出时没有显示申诉事由。
排查结果：申诉事由导出的是用车人的申诉事由，司机的申诉事由未导出。


1111111111问题53: 工单无法取消
时间: 2024年9月23日 08:34
工单号: 2109230423
排查过程：用户反馈工单结束多年后仍挂在代办中，无法取消。
排查结果：已处理，用户确认问题解决。


1111111111问题54: 短信下发失败
时间: 2024年10月15日 08:58
排查过程：用户反馈部分驾驶员和用车人未收到短信。
排查结果：服务器网络抖动导致短信未发出，已恢复。


1111111111问题55: 车辆里程统计误差
时间: 2024年10月18日 11:04
车牌号: 渝BA620Q
设备号: 25022400041462
排查过程：用户反馈车辆实际行驶里程与平台统计里程相差较大。
排查结果：平台里程计算基于GPS数据，隧道等无信号区域会导致里程计算误差。


1111111111问题56: 账号查询权限问题
时间: 2024年10月22日 17:31
排查过程：用户反馈无法查询客户账户信息。
排查结果：账号金库权限问题，需与4A系统对接处理。


1111111111问题57: 设备油耗统计问题
时间: 2024年10月29日 14:29
排查过程：用户反馈设备油耗统计默认值为7.0，无法设置为空。
排查结果：系统默认油耗为7.0，不支持设置为空。


1111111111问题58: 设备在线静止状态异常
时间: 2024年10月31日 09:47
设备号: 3613310110010210
排查过程：设备自10月25日后无数据回传，但系统仍显示为在线静止状态。
排查结果：DMS状态更新失败，需手动恢复。


1111111111问题59: 政企车队App闪退问题
时间: 2024年10月31日 15:31
排查过程：用户反馈三星手机使用政企车队App时闪退。
排查结果：系统兼容性问题，已修复并发布新版本。
1111111111问题60: 政企车队APP拍照闪退问题
时间: 2024年11月1日 09:07
排查过程：用户反馈华为和vivo手机在拍照时闪退，IOS正常。经过排查，发现是隐私合规整改中的权限问题。
排查结果：建议用户使用4.6.0版本，后续会尽快上线修复版本。如果已升级，需要卸载后扫码重装。


1111111111问题61: 设备轨迹异常问题
时间: 2024年11月5日 09:02
排查过程：用户反馈渝BN035Y设备轨迹异常，设备号25012100030593，轨迹显示与实际不符。经过排查，设备日志显示轨迹正常，但用户确认车辆未跑该段路。
排查结果：设备绑定记录正常，未发现换绑记录，可能是设备上报的CAN报文里程数据有问题。


1111111111问题62: 设备离线状态异常
时间: 2024年11月7日 12:39
排查过程：用户反馈设备25012100015084有定位报文回传，但平台显示离线。经过排查，设备上传的报文时间为2019年，导致平台未更新状态。
排查结果：设备端问题，建议重启设备。


1111111111问题63: 设备耗电问题
时间: 2024年11月6日 17:21
排查过程：用户反馈行车卫士协议设备耗电严重，希望调整设备上传频率。经过排查，政企车队的OMP平台不支持行车卫士协议的指令下发。
排查结果：建议通过脚本临时处理，后续会优化功能。


1111111111问题64: 设备视频无法查看
时间: 2024年11月13日 16:12
排查过程：用户反馈M55设备在厂家小程序查看视频正常，但在平台无法查看。经过排查，平台未收到设备的视频流。
排查结果：设备未推流，建议提工单进一步排查。


1111111111问题65: 设备轨迹分段异常
时间: 2024年11月14日 14:50
排查过程：用户反馈设备25012100008420有轨迹但没有分段。经过排查，设备上传的轨迹点速度变化不足，未形成分段。
排查结果：设备上传的轨迹点不足以形成分段轨迹。


1111111111问题66: 设备欠压告警未触发
时间: 2024年12月12日 09:50
排查过程：用户反馈R11设备欠压告警未触发。经过排查，平台未收到设备的欠压告警报文。
排查结果：平台不支持粤标协议，设备上传的报文格式错误。


1111111111问题67: 设备视频丢失告警
时间: 2024年12月17日 14:00
排查过程：用户反馈D35设备上报视频信号丢失告警，但设备为纯定位终端。经过排查，设备上传的报文包含1078相关报文。
排查结果：设备端问题，建议联系硬件组排查。


1111111111问题68: 设备离线状态异常
时间: 2024年12月11日 10:06
排查过程：用户反馈设备15131662439有报文上传，但平台显示离线。经过排查，设备状态未更新成功。
排查结果：设备状态更新失败，建议重启设备或刷新库。


1111111111问题69: 设备轨迹分段异常
时间: 2024年12月26日 14:56
排查过程：用户反馈设备70007139133、70007119799有历史轨迹但没有分段轨迹。经过排查，车牌号中间有空格，导致查询异常。
排查结果：车牌号录入时使用了空格或Tab键，建议修改车牌号。


1111111111问题70: 费用登记导入失败
时间: 2024年12月30日 09:17
排查过程：用户反馈费用登记批量导入失败，提示“批量导入重庆高速费用记录异常”。经过排查，模板格式不正确。
排查结果：模板格式错误，建议使用最新模板并确保数据符合规则。
1111111111问题71:重庆移动反馈车辆轨迹出现漂移导致里程增加
时间:2024年7月1日
排查过程：通过查询平台后台轨迹页面分析当天报文
排查结果：平台服务正常，设备网络异常无法与平台通信


1111111111问题72:重庆移动反馈审批员账号无法审批工单
时间:2024年7月3日
排查过程：检查账号权限和登录流程
排查结果：账号登录错误，需使用部门管理员账号进行审批


1111111111问题73:陕西移动反馈申请单子目的地无法选择
时间:2024年7月3日
排查过程：检查系统配置和地图选点功能
排查结果：地图选点功能失效，需更新配置


1111111111问题74:重庆移动反馈车辆轨迹未生成分段轨迹
时间:2024年7月11日
排查过程：检查设备报文推送情况
排查结果：设备未推送点熄火报文，需重新推送


1111111111问题75:车队平台电子栅栏新增的未在区域内触发，会报警吗，报警是在异常用车越界这个里面查看吗
时间:2025年1月7日
排查过程：跟驶入驶出告警不一样，产生了报警后，是归到越界这一类的
结果：会报警，6个小时以内只会产生一条“未在区域内触发”告警，报警是在异常用车里面查看


1111111111问题76:费用登记，各项求和的值与系统显示的合计值小数点后2位对不上
时间:2025年1月2日
排查过程：为客户导入的数据有多位小数，平台合计值是求和后四舍五入方式只取前2位，其他值是直接四舍五入取前2位，所以其他值求和与平台合计值有出入
排查结果：平台合计值是求和后四舍五入方式只取前2位


1111111111问题77:费用登记导入的车牌显示无匹配，增加该车牌号后，还是显示无匹配
时间:2025年1月2日
排查过程：因为费用登记所属部门是导入的那一时刻写入的，需要把之前的登记的信息重新导入一遍有部门了，不重新导入的话不会有部门的
排查结果：登记的信息重新导入一遍


1111111111问题78:重庆高速集团，车辆填写了车型、购置时间等信息，但在费用登记单个新增时，选中该车辆，不会带出相关信息
时间:2025年1月2日
排查过程：系统在设计逻辑是单条新增没做复填功能，只有编辑才有复填功能
排查结果：单条新增没做复填功能，只有编辑才有复填功能


1111111111问题79:11月申请的单子，预约12月使用，这种是算11月的出车任务还是12月的呢
时间:2025年1月2日
排查过程：根据申请的时间计算，出车任务归属到11月
排查结果：根据申请的时间计算，出车任务归属到11月


