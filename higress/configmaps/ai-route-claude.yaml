apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-claude
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "claude",
      "upstreams": [
        {
          "provider": "claude"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "PRE",
          "matchValue": "claude-"
        }
      ],
      "version": "1"
    }
