apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-moonshot
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "moonshot",
      "upstreams": [
        {
          "provider": "moonshot"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "PRE",
          "matchValue": "moonshot-"
        }
      ],
      "version": "1"
    }
