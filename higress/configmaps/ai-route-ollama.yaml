apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-ollama
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "ollama",
      "upstreams": [
        {
          "provider": "ollama"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "REGULAR",
          "matchValue": "codellama.*|llama.*"
        }
      ],
      "version": "1"
    }
