apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-openai
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "openai",
      "upstreams": [
        {
          "provider": "openai"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "REGULAR",
          "matchValue": "gpt-.*|o1-.*|o3-.*"
        }
      ],
      "version": "1"
    }
