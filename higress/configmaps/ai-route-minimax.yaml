apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-minimax
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "minimax",
      "upstreams": [
        {
          "provider": "minimax"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "PRE",
          "matchValue": "abab"
        }
      ],
      "version": "1"
    }
