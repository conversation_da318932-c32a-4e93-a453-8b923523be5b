apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-yi
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "yi",
      "upstreams": [
        {
          "provider": "yi"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "PRE",
          "matchValue": "yi-"
        }
      ],
      "version": "1"
    }
