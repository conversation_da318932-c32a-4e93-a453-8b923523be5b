apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-deepseek
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "deepseek",
      "upstreams": [
        {
          "provider": "deepseek"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "PRE",
          "matchValue": "deepseek"
        }
      ],
      "version": "1"
    }
