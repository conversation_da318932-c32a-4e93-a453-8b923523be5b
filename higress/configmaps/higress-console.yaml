apiVersion: v1
data:
  dashboard.builtin: "true"
  index.redirect-target: /ai/route
  mode: standalone
  route.default.initialized: "true"
  system.initialized: "true"
kind: ConfigMap
metadata:
  creationTimestamp: "2000-01-01T00:00:00Z"
  labels:
    higress.io/resource-definer: higress
  managedFields:
  - apiVersion: v1
    fieldsType: FieldsV1
    fieldsV1:
      f:data:
        f:dashboard.builtin: {}
        f:route.default.initialized: {}
        f:system.initialized: {}
    manager: Kubernetes Java Client
    operation: Update
    time: "2025-06-16T09:26:05Z"
  name: higress-console
  namespace: higress-system
  resourceVersion: "4"
