apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-cohere
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "cohere",
      "upstreams": [
        {
          "provider": "cohere"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "REGULAR",
          "matchValue": "command|command-.*"
        }
      ],
      "version": "1"
    }
