apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-gemini
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "gemini",
      "upstreams": [
        {
          "provider": "gemini"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "PRE",
          "matchValue": "gemini-"
        }
      ],
      "version": "1"
    }
