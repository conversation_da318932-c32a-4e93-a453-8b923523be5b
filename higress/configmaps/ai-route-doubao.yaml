apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-doubao
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "doubao",
      "upstreams": [
        {
          "provider": "doubao"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "PRE",
          "matchValue": "doubao-"
        }
      ],
      "version": "1"
    }
