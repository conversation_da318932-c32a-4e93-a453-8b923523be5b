apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    higress.io/config-map-type: ai-route
    higress.io/resource-definer: higress
  name: ai-route-baidu
  namespace: higress-system
  resourceVersion: "1"
data:
  data: |
    {
      "name": "baidu",
      "upstreams": [
        {
          "provider": "baidu"
        }
      ],
      "modelPredicates": [
        {
          "matchType": "PRE",
          "matchValue": "ERNIE-"
        }
      ],
      "version": "1"
    }
