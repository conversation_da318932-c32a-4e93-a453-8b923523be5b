apiVersion: v1
kind: ConfigMap
metadata:
  annotations:
    control-plane.alpha.kubernetes.io/leader: '{"holderIdentity":"higress-pilot","holderKey":"default","leaseDurationSeconds":30,"acquireTime":"2025-06-16T09:25:40Z","renewTime":"2025-06-18T09:30:01Z","leaderTransitions":0}'
  creationTimestamp: "2025-06-16T09:25:40Z"
  managedFields:
  - apiVersion: v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:control-plane.alpha.kubernetes.io/leader: {}
    manager: pilot-discovery
    operation: Update
    time: "2025-06-18T09:30:01Z"
  name: higress-namespace-controller-election
  namespace: higress-system
  resourceVersion: "9250"
