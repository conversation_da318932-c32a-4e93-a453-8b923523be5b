apiVersion: v1
data:
  cert: |
    acmeIssuer:
    - ak: ""
      email: <EMAIL>
      name: letsencrypt
      sk: ""
    automaticHttps: true
    credentialConfig: []
    fallbackForInvalidSecret: false
    renewBeforeDays: 30
    version: "20250616092532"
kind: ConfigMap
metadata:
  creationTimestamp: "2025-06-16T09:25:32Z"
  managedFields:
  - apiVersion: v1
    fieldsType: FieldsV1
    fieldsV1:
      f:data:
        .: {}
        f:cert: {}
    manager: higress
    operation: Update
    time: "2025-06-16T09:25:32Z"
  name: higress-https
  namespace: higress-system
  resourceVersion: "1"
