apiVersion: v1
data:
  adminDisplayName: YWRtaW4=
  adminPassword: dUMmPkknREZMSXVsc1FGSigoPyY=
  adminUsername: YWRtaW4=
  iv: e243aTM4Z0trK0ZLR0t1bA==
  key: W2IuMGkxOygwekFgeWs9PD4pazZyekFWVXh7XjBrfUk=
kind: Secret
metadata:
  creationTimestamp: "2000-01-01T00:00:00Z"
  labels:
    higress.io/resource-definer: higress
  managedFields:
  - apiVersion: v1
    fieldsType: FieldsV1
    fieldsV1:
      f:data:
        .: {}
        f:adminDisplayName: {}
        f:adminPassword: {}
        f:adminUsername: {}
        f:iv: {}
        f:key: {}
      f:metadata:
        f:labels:
          .: {}
          f:higress.io/resource-definer: {}
    manager: Kubernetes Java Client
    operation: Update
    time: "2025-06-16T09:26:05Z"
  name: higress-console
  namespace: higress-system
  resourceVersion: "2"
type: Opaque
