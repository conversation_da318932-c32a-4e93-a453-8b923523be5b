apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    higress.io/destination: "llm-ollama.internal.dns:11434"
    higress.io/ignore-path-case: "false"
    higress.io/regex-match-header-x-higress-llm-model: "codellama.*|llama.*"
  labels:
    higress.io/resource-definer: higress
  name: ai-route-ollama.internal
  namespace: higress-system
  resourceVersion: "1"
spec:
  ingressClassName: higress
  rules:
  - http:
      paths:
      - backend:
          resource:
            apiGroup: networking.higress.io
            kind: McpBridge
            name: default
        path: /
        pathType: Prefix
