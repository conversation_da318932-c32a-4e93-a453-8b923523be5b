apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    higress.io/destination: "llm-doubao.internal.dns:443"
    higress.io/ignore-path-case: "false"
    higress.io/prefix-match-header-x-higress-llm-model: "doubao-"
  labels:
    higress.io/resource-definer: higress
  name: ai-route-doubao.internal
  namespace: higress-system
  resourceVersion: "1"
spec:
  ingressClassName: higress
  rules:
  - http:
      paths:
      - backend:
          resource:
            apiGroup: networking.higress.io
            kind: McpBridge
            name: default
        path: /
        pathType: Prefix
