apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    higress.io/destination: "llm-cohere.internal.dns:443"
    higress.io/ignore-path-case: "false"
    higress.io/regex-match-header-x-higress-llm-model: "command|command-.*"
  labels:
    higress.io/resource-definer: higress
  name: ai-route-cohere.internal
  namespace: higress-system
  resourceVersion: "1"
spec:
  ingressClassName: higress
  rules:
  - http:
      paths:
      - backend:
          resource:
            apiGroup: networking.higress.io
            kind: McpBridge
            name: default
        path: /
        pathType: Prefix
