apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: higress-gateway-global-custom-response
  namespace: higress-system
  creationTimestamp: "2000-01-01T00:00:00Z"
  resourceVersion: "1"
  labels:
    app: higress-gateway
    higress: higress-system-higress-gateway
spec:
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: GATEWAY
      listener:
        filterChain:
          filter:
            name: envoy.filters.network.http_connection_manager
    patch:
      operation: INSERT_FIRST
      value:
        name: envoy.filters.http.custom_response
        typed_config:
          '@type': type.googleapis.com/envoy.extensions.filters.http.custom_response.v3.CustomResponse
