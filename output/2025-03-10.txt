[2025-03-10 08:44:42] 王安平在群里说：@薛超 薛老师，有个查询D32激活时间与最后定位时间的单子，麻烦处理下，http://172.21.96.206:18080/projex/#/work/ZNYJRJ-3062

[2025-03-10 08:49:22] 薛超在群里说：要得  我看下来

[2025-03-10 08:51:10] 胡军建在群里说：@陈皎 陈老师，oneway 上也没有过滤，6号这个卫星数是0，但是也是上传展示了的。

[2025-03-10 08:51:11] 胡军建在群里说：{"filename":"3cf702d34e289096370e736a9cf34810.png","size":"39334","mime_type":"image/png","thumbnail_width":"200","thumbnail_url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTM3N2UwNTFlOWQ2M2ZmZGRjNjhk","url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTM3N2UwNTFlOWQ2M2ZmZGRjNjhj","hash":"3C:F7:02:D3:4E:28:90:96:37:0E:73:6A:9C:F3:48:10","thumbnail_height":"62"}

[2025-03-10 08:52:30] 陈皎在群里说：101的定位方式就是后期会被过滤掉的

[2025-03-10 08:53:39] 胡军建在群里说：@陈皎 你说的后期是说，后面平台升级之后，这种数据会过滤掉？

[2025-03-10 08:54:34] 陈皎在群里说：[失望]后面的流程处理 不是后期升级

[2025-03-10 08:55:49] 胡军建在群里说：但现在的问题就是这个数据没有被过滤掉，oneway上展示了，用户App端也展示了。

[2025-03-10 08:56:15] 胡军建在群里说：{"filename":"image_fded3caa445f48ed0b230f31412304a6.jpeg","size":"1122444","mime_type":"image/png","thumbnail_width":"92","thumbnail_url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTM4YWYwNWNmY2U3YzI2Mjk3ZGM0","url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTM4YWYwNWNmY2U3YzI2Mjk3ZGMz","hash":"85:23:56:40:AD:27:F7:92:6F:18:2A:E3:91:1A:6D:53","thumbnail_height":"200"}

[2025-03-10 08:56:21] 胡军建在群里说：{"filename":"fa27e7230f501e903699c0a20a2537ff.png","size":"251587","mime_type":"image/png","thumbnail_width":"200","thumbnail_url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTM4YjQyMjgxNzczOTc5YjE1ZDgx","url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTM4YjQyMjgxNzczOTc5YjE1ZDgw","hash":"FA:27:E7:23:0F:50:1E:90:36:99:C0:A2:0A:25:37:FF","thumbnail_height":"125"}

[2025-03-10 08:58:42] 陈皎在群里说：设备号给一下，我们看一下

[2025-03-10 08:59:30] 胡军建在群里说：15131664674，看3月6号的数据。我周六的时候发了报文的。@陈皎 

[2025-03-10 09:03:41] 陈皎在群里说：@王来鹏 这个看一下是调的那个接口

[2025-03-10 09:15:25] 王来鹏在群里说：@蒋昭玄 昭玄老师看看呢

[2025-03-10 09:16:17] 蒋昭玄在群里说：queryVehicleDriverLocation

[2025-03-10 09:17:42] 熊飞在群里说：15856301968 这个号码帮忙看下，上周开的新账号，但是获取验证码提示未知错误

[2025-03-10 09:18:02] 熊飞在群里说：{"filename":"dae9751f601415d09edb6f001054f34f.png","size":"96545","mime_type":"image/png","thumbnail_width":"200","thumbnail_url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTNkYzkwNWNmY2U3YzI2Mjk3ZTkx","url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTNkYzkwNWNmY2U3YzI2Mjk3ZTkw","hash":"DA:E9:75:1F:60:14:15:D0:9E:DB:6F:00:10:54:F3:4F","thumbnail_height":"165"}

[2025-03-10 09:18:16] 王超在群里说：好

[2025-03-10 09:18:36] 熊飞在群里说： 是这个原因吗？

[2025-03-10 09:18:37] 熊飞在群里说：{"filename":"e0947da724da6f72cb2ac1311d62a824.png","size":"15680","mime_type":"image/png","thumbnail_width":"200","thumbnail_url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTNkZWM3ZmU0Mjk0ZDY1NDMzZWM5","url":"https://cq.moazq.chinamobile.com/v1/origin/file/upload/file/downOrigin/NjdjZTNkZWM3ZmU0Mjk0ZDY1NDMzZWM4","hash":"E0:94:7D:A7:24:DA:6F:72:CB:2A:C1:31:1D:62:A8:24","thumbnail_height":"36"}

[2025-03-10 09:18:47] 王超在群里说：是得

[2025-03-10 09:19:02] 熊飞在群里说：那这个我上周开的账号为什么可以成功呢

[2025-03-10 09:19:17] 熊飞在群里说：系统不是不能同一手机号存在吗

[2025-03-10 09:26:31] 王超在群里说：OMP新增企业时自动创建的用户未校验

[2025-03-10 09:27:51] 熊飞在群里说：好的，看是否可以校验成以前的，以前系统中存在就不能创建了。

[2025-03-10 09:28:16] 王超在群里说：这个后面看看优化下

[2025-03-10 09:28:37] 熊飞在群里说：好的 感谢

[2025-03-10 09:38:42] 王来鹏在群里说：查询的dms

[2025-03-10 17:16:17] 胡军建在群里说：@陈皎 @王来鹏 两位老师，有确认到为什么那个101的定位方式没有被过滤掉吗。

[2025-03-10 17:17:42] 王来鹏在群里说：目前确实没有过滤

[2025-03-10 17:19:30] 陈皎在群里说：这个沟通过了 平台就是这样设计的，保存了最后一次设备上报的数据  提个需求到那个在线文档吧，看产品老师要不要排期搞

[2025-03-10 17:20:41] 胡军建在群里说：好的

[2025-03-10 17:25:17] 胡军建在群里说：平台确实有过滤卫星信号差的定位数据，但是不会过滤设备最后一次上传的定位数据。我理解的没错吧

[2025-03-10 17:25:21] 胡军建在群里说：@陈皎 

[2025-03-10 17:27:58] 陈龙在群里说：这种数据会有被过滤的标记，但是出现这种情况展示上一个有效位置也有用户曾经提出过疑问，所以后面决定还是展示设备上报的真实位置，即使有异常也能看出来

