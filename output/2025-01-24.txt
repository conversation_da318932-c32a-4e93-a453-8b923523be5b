[2025-01-24 08:42:28] 王超在群里说：抽查了两个重庆高速的m55设备，视频默认子码流播放，未发现昨天那种异常卡顿现象，请售后同事也帮忙留意一下

[2025-01-24 09:12:22] 王安平在群里说：在线的几台车都看了下，都比较流畅了。有台车比较卡顿中间出现连不上平台的情况，可能设备自动重启了，现在看也比较流畅了，我们再观察下

[2025-01-24 09:12:56] 王超在群里说：清晰度能接受不，子码流就是清晰度不够

[2025-01-24 09:35:29] 王安平在群里说：跟客户没提清晰度的事情，客户看了下流程些了，没说清晰度

[2025-01-24 09:57:04] 章建威在群里说：@王安平 ，帮忙记录到售后反馈的表格标红下，2月份与业务版本一起上线

[2025-01-24 09:57:57] 王安平在群里说：好的

[2025-01-24 10:12:52] 阮祥兵在群里说：@所有人  这个问题引出的几个问题，需要持续跟进和优化，@王铭磊  铭磊节后牵头整理讨论。
1、默认码流问题：根据协议选择默认码流是否合理？ 默认码流是否可以按型号，按设备配置，而不是代码写死。  @王铭磊  @黄保山 @章建威 
2、主码流卡顿问题：是只有M55主码流才卡，还是全部设备主码流都卡。 卡的原因是平台的带宽问题，还是物联网上行速度问题。 @王超 @杨鹏博 
3、新终端测试问题：持续丰富需要测试的场景和用例。  @王超 
4、rtvs自主可控问题：除代码开发外的其他工作，我们是否可以替代供应商了。  细节掌握的怎么样了。  @王茂源 

[2025-01-24 10:13:28] 王茂源在群里说：收到

[2025-01-24 10:13:35] 王超在群里说：收到

[2025-01-24 10:28:19] 王铭磊在群里说：收到

[2025-01-24 10:31:37] 章建威在群里说：收到

