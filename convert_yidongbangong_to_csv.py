import csv
import re

# 读取原始文件
input_file = 'summary-yidongbangong.txt'
output_file = 'output_yidongbangong.csv'

with open(input_file, 'r', encoding='utf-8') as f:
    text = f.read()

# 按照每个问题分割
entries = re.split(r'1111111111问题\d+:', text)[1:]  # 去掉最开始的空内容

questions = []
answers = []

for entry in entries:
    # 提取问题标题（第一行）和排查结果（最后的排查结果部分）
    lines = entry.strip().splitlines()
    if not lines:
        continue

    # 第一行一般是问题描述
    first_line = lines[0].strip()

    # 查找排查结果
    answer_text = ""
    for line in lines:
        if '排查结果' in line:
            answer_text = line.replace('排查结果：', '').strip()
            break

    questions.append(first_line)
    answers.append(answer_text)

# 写入csv
with open(output_file, 'w', encoding='utf-8', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(['问题', '答案'])  # 写表头
    writer.writerows(zip(questions, answers))

print(f"成功保存到 {output_file}")