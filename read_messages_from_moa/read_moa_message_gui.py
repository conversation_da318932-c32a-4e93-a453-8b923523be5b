import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pysqlcipher3 import dbapi2 as sqlite
import datetime
import os
import time
import threading

def connect_to_encrypted_db(db_path, password):
    conn = sqlite.connect(db_path)
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA key = '{password}'")
    cursor.execute("PRAGMA cipher_compatibility = 4")
    return conn, cursor

def unix_to_datetime(unix_time):
    timestamp = unix_time / 1000
    dt = datetime.datetime.fromtimestamp(timestamp)
    formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    return formatted_time

def datetime_to_unix(dt_str):
    try:
        dt = datetime.datetime.strptime(dt_str, "%Y-%m-%d")
        return int(time.mktime(dt.timetuple())) * 1000
    except ValueError:
        return None

def write_message(file_path, message):
    with open(file_path, 'a', encoding='utf-8') as file:
        file.write(message)

class MessageExporterApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("MOA 消息导出工具")
        self.geometry("600x450")
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(2, weight=1)

        # --- Main Frames ---
        db_frame = ttk.LabelFrame(self, text="数据库连接设置", padding=(10, 5))
        db_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        db_frame.grid_columnconfigure(1, weight=1)

        params_frame = ttk.LabelFrame(self, text="导出参数", padding=(10, 5))
        params_frame.grid(row=1, column=0, padx=10, pady=5, sticky="ew")
        params_frame.grid_columnconfigure(1, weight=1)

        action_frame = ttk.Frame(self, padding=(10, 5))
        action_frame.grid(row=2, column=0, padx=10, pady=10, sticky="ew")
        action_frame.grid_columnconfigure(0, weight=1)

        # --- Widgets ---

        # --- DB Connection Frame ---
        self.db_path_label = ttk.Label(db_frame, text="数据库文件路径:")
        self.db_path_var = tk.StringVar()
        self.db_path_entry = ttk.Entry(db_frame, textvariable=self.db_path_var)
        self.db_path_button = ttk.Button(db_frame, text="浏览...", command=self.browse_db_file)

        self.key_label = ttk.Label(db_frame, text="Key (密码):")
        self.key_var = tk.StringVar()
        self.key_entry = ttk.Entry(db_frame, textvariable=self.key_var, show="*")

        # --- Parameters Frame ---
        self.group_label = ttk.Label(params_frame, text="选择群组:")
        self.group_var = tk.StringVar(value="1")
        group_radio_frame = ttk.Frame(params_frame)
        self.group_radio1 = ttk.Radiobutton(group_radio_frame, text="战建协同群", variable=self.group_var, value="1")
        self.group_radio2 = ttk.Radiobutton(group_radio_frame, text="车队售后群", variable=self.group_var, value="2")

        self.start_time_label = ttk.Label(params_frame, text="消息起始时间 (YYYY-MM-DD):")
        self.start_time_var = tk.StringVar(value=datetime.date.today().strftime("%Y-%m-%d"))
        self.start_time_entry = ttk.Entry(params_frame, textvariable=self.start_time_var)

        self.save_path_label = ttk.Label(params_frame, text="保存路径:")
        self.save_path_var = tk.StringVar()
        self.save_path_entry = ttk.Entry(params_frame, textvariable=self.save_path_var)
        self.save_path_button = ttk.Button(params_frame, text="浏览...", command=self.browse_save_file)

        # --- Action Frame ---
        self.export_button = ttk.Button(action_frame, text="开始导出", command=self.start_export_thread)
        self.status_var = tk.StringVar(value="准备就绪")
        self.status_label = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)

        # --- Layout ---
        # DB Frame Layout
        self.db_path_label.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.db_path_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)
        self.db_path_button.grid(row=0, column=2, padx=5, pady=5)
        self.key_label.grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.key_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky=tk.EW)

        # Params Frame Layout
        self.group_label.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        group_radio_frame.grid(row=0, column=1, columnspan=2, padx=5, pady=5, sticky=tk.W)
        self.group_radio1.pack(side=tk.LEFT, padx=(0, 10))
        self.group_radio2.pack(side=tk.LEFT)
        self.start_time_label.grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.start_time_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky=tk.EW)
        self.save_path_label.grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.save_path_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.EW)
        self.save_path_button.grid(row=2, column=2, padx=5, pady=5)

        # Action Frame Layout
        self.export_button.grid(row=0, column=0, pady=10)
        self.status_label.grid(row=3, column=0, sticky="ew", padx=10, pady=5)

    def browse_db_file(self):
        filepath = filedialog.askopenfilename(
            title="选择数据库文件",
            filetypes=(("Database files", "*.db"), ("All files", "*.*"))
        )
        if filepath:
            self.db_path_var.set(filepath)

    def browse_save_file(self):
        start_time_str = self.start_time_var.get() or "export"
        default_filename = f"moa_message_start_{start_time_str}.txt"
        filepath = filedialog.asksaveasfilename(
            title="保存消息文件",
            initialfile=default_filename,
            defaultextension=".txt",
            filetypes=(("Text files", "*.txt"), ("All files", "*.*"))
        )
        if filepath:
            self.save_path_var.set(filepath)

    def start_export_thread(self):
        self.export_button.config(state=tk.DISABLED)
        self.status_var.set("正在导出...")
        thread = threading.Thread(target=self.export_messages, daemon=True)
        thread.start()

    def export_messages(self):
        db_path = self.db_path_var.get()
        password = self.key_var.get()
        group_choice = self.group_var.get()
        start_time_str = self.start_time_var.get()
        output_file = self.save_path_var.get()

        if not all([db_path, password, start_time_str, output_file]):
            messagebox.showerror("错误", "所有字段均为必填项。")
            self.status_var.set("错误：缺少输入。")
            self.export_button.config(state=tk.NORMAL)
            return

        start_time_unix = datetime_to_unix(start_time_str)
        if start_time_unix is None:
            messagebox.showerror("错误", "日期格式无效，请使用 YYYY-MM-DD。")
            self.status_var.set("错误：日期格式无效。")
            self.export_button.config(state=tk.NORMAL)
            return

        table_name = "im_msg_group_121165" if group_choice == "1" else "im_msg_group_110853"

        try:
            # Clear the output file if it exists
            if os.path.exists(output_file):
                open(output_file, 'w').close()

            conn, cursor = connect_to_encrypted_db(db_path, password)
            cursor.execute(f"SELECT create_time, name, content FROM {table_name} WHERE message_type=2 AND create_time >= ? AND content_type IN ('template/application/at', 'text', 'image')", (start_time_unix,))
            messages = cursor.fetchall()

            for msg in messages:
                message = f"[{unix_to_datetime(msg[0])}] {msg[1]}在群里说：{msg[2]}\n"
                write_message(output_file, message)

            self.status_var.set(f"导出完成！共 {len(messages)} 条消息。文件：{os.path.abspath(output_file)}")
            messagebox.showinfo("成功", f"导出完成！\n共导出 {len(messages)} 条消息。\n文件保存在：{os.path.abspath(output_file)}")

        except sqlite.Error as e:
            self.status_var.set(f"数据库错误: {e}")
            messagebox.showerror("数据库错误", f"发生错误: {e}")
        except Exception as e:
            self.status_var.set(f"发生未知错误: {e}")
            messagebox.showerror("错误", f"发生未知错误: {e}")
        finally:
            if 'conn' in locals() and conn:
                conn.close()
            self.export_button.config(state=tk.NORMAL)


if __name__ == "__main__":
    app = MessageExporterApp()
    app.mainloop()