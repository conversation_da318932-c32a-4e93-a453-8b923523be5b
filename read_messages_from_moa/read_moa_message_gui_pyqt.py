import sys
import datetime
import os
import time
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGridLayout, QGroupBox, QLabel, QLineEdit, QPushButton,
    QRadioButton, QFileDialog, QMessageBox, QStatusBar, QDateEdit
)
from PyQt6.QtCore import QObject, QThread, pyqtSignal, pyqtSlot, QDate
from pysqlcipher3 import dbapi2 as sqlite

# --- Helper Functions (unchanged) ---
def connect_to_encrypted_db(db_path, password):
    """Establishes a connection to an encrypted SQLite database."""
    conn = sqlite.connect(db_path)
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA key = '{password}'")
    cursor.execute("PRAGMA cipher_compatibility = 4")
    return conn, cursor

def unix_to_datetime(unix_time):
    """Converts a Unix timestamp (in milliseconds) to a formatted datetime string."""
    timestamp = unix_time / 1000
    dt = datetime.datetime.fromtimestamp(timestamp)
    formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    return formatted_time

def datetime_to_unix(dt_str):
    """Converts a YYYY-MM-DD string to a Unix timestamp (in milliseconds)."""
    try:
        dt = datetime.datetime.strptime(dt_str, "%Y-%m-%d")
        return int(time.mktime(dt.timetuple())) * 1000
    except ValueError:
        return None

def write_message(file_path, message):
    """Appends a message to a file."""
    with open(file_path, 'a', encoding='utf-8') as file:
        file.write(message)

# --- Worker for Threading ---
class ExportWorker(QObject):
    """
    Runs the message export task in a separate thread to keep the GUI responsive.
    """
    finished = pyqtSignal()
    error = pyqtSignal(str)
    success = pyqtSignal(str)
    progress = pyqtSignal(str)

    def __init__(self, db_path, password, group_choice, start_time_str, output_file):
        super().__init__()
        self.db_path = db_path
        self.password = password
        self.group_choice = group_choice
        self.start_time_str = start_time_str
        self.output_file = output_file

    @pyqtSlot()
    def run(self):
        """Starts the export process."""
        if not all([self.db_path, self.password, self.start_time_str, self.output_file]):
            self.error.emit("所有字段均为必填项。")
            self.finished.emit()
            return

        start_time_unix = datetime_to_unix(self.start_time_str)
        if start_time_unix is None:
            self.error.emit("日期格式无效，请使用 YYYY-MM-DD。")
            self.finished.emit()
            return

        table_name = "im_msg_group_121165" if self.group_choice == "1" else "im_msg_group_110853"
        conn = None
        try:
            self.progress.emit("正在连接数据库...")
            if os.path.exists(self.output_file):
                open(self.output_file, 'w').close()

            conn, cursor = connect_to_encrypted_db(self.db_path, self.password)
            self.progress.emit("正在查询和导出消息...")
            
            cursor.execute(f"SELECT create_time, name, content FROM {table_name} WHERE message_type=2 AND create_time >= ? AND content_type IN ('template/application/at', 'text', 'image')", (start_time_unix,))
            messages = cursor.fetchall()

            for msg in messages:
                message = f"[{unix_to_datetime(msg[0])}] {msg[1]}在群里说：{msg[2]}\n"
                write_message(self.output_file, message)
            
            success_message = f"导出完成！共 {len(messages)} 条消息。文件：{os.path.abspath(self.output_file)}"
            self.success.emit(success_message)

        except sqlite.Error as e:
            self.error.emit(f"数据库错误: {e}")
        except Exception as e:
            self.error.emit(f"发生未知错误: {e}")
        finally:
            if conn:
                conn.close()
            self.finished.emit()


# --- Main Application Window ---
class MessageExporterApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MOA 消息导出工具 (PyQt6)")
        self.setGeometry(100, 100, 600, 450)

        # --- Main Widget and Layout ---
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # --- Database Connection Group ---
        db_group = QGroupBox("数据库连接设置")
        db_layout = QGridLayout(db_group)
        
        self.db_path_label = QLabel("数据库文件路径:")
        self.db_path_entry = QLineEdit()
        self.db_path_button = QPushButton("浏览...")
        self.db_path_button.clicked.connect(self.browse_db_file)

        self.key_label = QLabel("Key (密码):")
        self.key_entry = QLineEdit()
        self.key_entry.setEchoMode(QLineEdit.EchoMode.Password)

        db_layout.addWidget(self.db_path_label, 0, 0)
        db_layout.addWidget(self.db_path_entry, 0, 1)
        db_layout.addWidget(self.db_path_button, 0, 2)
        db_layout.addWidget(self.key_label, 1, 0)
        db_layout.addWidget(self.key_entry, 1, 1, 1, 2)

        # --- Export Parameters Group ---
        params_group = QGroupBox("导出参数")
        params_layout = QGridLayout(params_group)

        self.group_label = QLabel("选择群组:")
        self.group_radio1 = QRadioButton("战建协同群")
        self.group_radio1.setChecked(True)
        self.group_radio2 = QRadioButton("车队售后群")
        group_radio_layout = QHBoxLayout()
        group_radio_layout.addWidget(self.group_radio1)
        group_radio_layout.addWidget(self.group_radio2)
        group_radio_layout.addStretch()

        self.start_time_label = QLabel("消息起始时间 (YYYY-MM-DD):")
        self.start_time_entry = QDateEdit()
        self.start_time_entry.setCalendarPopup(True)
        self.start_time_entry.setDate(QDate.currentDate())
        self.start_time_entry.setDisplayFormat("yyyy-MM-dd")

        self.save_path_label = QLabel("保存路径:")
        self.save_path_entry = QLineEdit()
        self.save_path_button = QPushButton("浏览...")
        self.save_path_button.clicked.connect(self.browse_save_file)

        params_layout.addWidget(self.group_label, 0, 0)
        params_layout.addLayout(group_radio_layout, 0, 1, 1, 2)
        params_layout.addWidget(self.start_time_label, 1, 0)
        params_layout.addWidget(self.start_time_entry, 1, 1, 1, 2)
        params_layout.addWidget(self.save_path_label, 2, 0)
        params_layout.addWidget(self.save_path_entry, 2, 1)
        params_layout.addWidget(self.save_path_button, 2, 2)

        # --- Action Area ---
        action_layout = QVBoxLayout()
        self.export_button = QPushButton("开始导出")
        self.export_button.clicked.connect(self.start_export)
        action_layout.addWidget(self.export_button)
        action_layout.addStretch()

        # --- Status Bar ---
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备就绪")

        # --- Add all groups to main layout ---
        main_layout.addWidget(db_group)
        main_layout.addWidget(params_group)
        main_layout.addLayout(action_layout)

    def browse_db_file(self):
        filepath, _ = QFileDialog.getOpenFileName(
            self,
            "选择数据库文件",
            "",
            "Database files (*.db);;All files (*.*)"
        )
        if filepath:
            self.db_path_entry.setText(filepath)

    def browse_save_file(self):
        start_time_str = self.start_time_entry.date().toString("yyyy-MM-dd") or "export"
        default_filename = f"moa_message_start_{start_time_str}.txt"
        filepath, _ = QFileDialog.getSaveFileName(
            self,
            "保存消息文件",
            default_filename,
            "Text files (*.txt);;All files (*.*)"
        )
        if filepath:
            self.save_path_entry.setText(filepath)

    def start_export(self):
        self.export_button.setEnabled(False)
        self.status_bar.showMessage("正在导出...")

        db_path = self.db_path_entry.text()
        password = self.key_entry.text()
        group_choice = "1" if self.group_radio1.isChecked() else "2"
        start_time_str = self.start_time_entry.date().toString("yyyy-MM-dd")
        output_file = self.save_path_entry.text()

        # Setup worker and thread
        self.thread = QThread()
        self.worker = ExportWorker(db_path, password, group_choice, start_time_str, output_file)
        self.worker.moveToThread(self.thread)

        # Connect signals and slots
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.thread.finished.connect(self.thread.deleteLater)
        self.worker.error.connect(self.on_export_error)
        self.worker.success.connect(self.on_export_success)
        self.worker.progress.connect(self.status_bar.showMessage)
        
        # Start the thread
        self.thread.start()
        
        # Final UI updates on finish
        self.thread.finished.connect(
            lambda: self.export_button.setEnabled(True)
        )

    def on_export_error(self, message):
        QMessageBox.critical(self, "错误", message)
        self.status_bar.showMessage(f"错误: {message}")

    def on_export_success(self, message):
        QMessageBox.information(self, "成功", message)
        self.status_bar.showMessage(message)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    main_win = MessageExporterApp()
    main_win.show()
    sys.exit(app.exec())