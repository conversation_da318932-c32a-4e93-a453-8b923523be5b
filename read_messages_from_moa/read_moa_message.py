from pysqlcipher3 import dbapi2 as sqlite
import datetime
import os
import time

def connect_to_encrypted_db(db_path, password):
    conn = sqlite.connect(db_path)
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA key = '{password}'")
    cursor.execute("PRAGMA cipher_compatibility = 4")
    return conn, cursor

def unix_to_datetime(unix_time):
    timestamp = unix_time / 1000  
    dt = datetime.datetime.fromtimestamp(timestamp)
    formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")   
    return formatted_time

def datetime_to_unix(dt):
    dt = datetime.datetime.strptime(dt, "%Y-%m-%d")
    timestamp = int(time.mktime(dt.timetuple())) * 1000
    return timestamp

def write_message(file_path, message):
    with open(file_path, 'a', encoding='utf-8') as file:
        file.write(f"{message}")

def main():
    print("MOA消息导出工具")
    print("="*50)
    
    db_path = input("请输入数据库文件路径：").strip('"') if input("请输入数据库文件路径：") else '/Users/<USER>/Documents/7b70f602d319c17fc85899aa18e30965.db'
    # 陈皎
    # password = 'f36fc043b9a393b6691c5b3865ec1dc91dc01edc59d82f9b580c9f1ccba91f39'
    print("\n请选择要导出的群：")
    print("1. 战建协同群")
    print("2. 车队售后群")
    choice = input("请输入选择（1或2）：") 
    choice = choice.strip('"') if choice else "1"
    
    table_name = "im_msg_group_121165" if choice == "1" else "im_msg_group_110853"
    
    print("\n请输入key：")
    choice1 = input("请输入选择：")
    password = choice1.strip('"') if choice1 else 'f36fc043b9a393b6691c5b3865ec1dc91dc01edc59d82f9b580c9f1ccba91f39' 
    print("\n请输入消息的起始时间：")
    start_time = input("请输入xxxx-xx-xx：")
    start_time = start_time.strip('"') 
    print("\n请输入导出文件：")
    output_file = input("请输入：").strip('"')  if input("请输入导出文件：") else f"moa_message_start_{start_time}.txt"

    start_time = datetime_to_unix(start_time)
    try:
        conn, cursor = connect_to_encrypted_db(db_path, password)
        cursor.execute(f"SELECT create_time,name,content FROM {table_name} where message_type=2 and create_time >={start_time} and content_type in ('template/application/at','text','image')")
        messages = cursor.fetchall()
        
        print(f"\n开始导出消息到 {output_file}")
        for msg in messages:
            message = f"[{unix_to_datetime(msg[0])}] {msg[1]}在群里说：{msg[2]}\n"
            write_message(output_file, message)
        
        print(f"导出完成！共导出 {len(messages)} 条消息")
        print(f"文件保存在：{os.path.abspath(output_file)}")
        
    except sqlite.Error as e:
        print(f"错误: {e}")
    finally:
        if 'conn' in locals():
            conn.close()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()