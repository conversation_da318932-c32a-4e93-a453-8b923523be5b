

###问题:关于D35终端（序列号18070014995）超速报警机制及轨迹漂移误报问题  
答复：平台当前超速报警判断逻辑为瞬时超速即触发（无连续超速判断），轨迹漂移不会触发告警。但用户反馈存在因漂移触发告警的情况，经检查efence日志确认报警属实。目前平台未配置超速持续时间参数，新版本已在测试中并计划下周上线连续超速判断功能。

-----------------------------------------

###问题:用户里程统计异常（实际15公里/平台170公里）  
答复：异常原因系设备部分CAN报文未上报总里程数据导致平台计算误差。已对该条轨迹数据临时修正处理，平台后续版本将增加针对异常CAN报文的判断机制。目前团队讨论拟优先采用GPS里程计算或与CAN数据交叉验证方案，但需进一步测试再确定最终处理逻辑。



create user 'xxljob'@'172.21.26.%' identified by 'hAK1rPD2UhS6J1&MQs8Az';
grant all privileges on  xxl_job.*  to 'xxljob'@'172.21.26.%';
flush privileges;





docker run \
--name skywalking-oap \
--restart always \
-p 11800:11800 \
-p 12800:12800 -d \
--privileged=true \
-e TZ=Asia/Shanghai \
-e SW_STORAGE=elasticsearch \
-e SW_STORAGE_ES_CLUSTER_NODES=1************:9200,172.21.26.119:9200,172.21.26.118:9200 \
-e SW_ES_USER=elastic \
-e SW_ES_PASSWORD=elasticsearch2025@w2ld \
-e SW_STORAGE_ES_LIFECYCLE_ENABLED=true \
-e SW_STORAGE_ES_LIFECYCLE_ROLLOVER_ENABLED=true \
-e SW_STORAGE_ES_LIFECYCLE_ROLLOVER_PERIOD=1d \
-e SW_STORAGE_ES_LIFECYCLE_CLEAN_TASK_ENABLED=true \
-e SW_STORAGE_ES_LIFECYCLE_CLEAN_TTL=3d \
-v /etc/localtime:/etc/localtime:ro \
swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/apache/skywalking-oap-server:10.1.0



docker run \
--name skywalking-ui \
--restart always \
-p 8091:8080 -d \
--privileged=true \
--link skywalking-oap:skywalking-oap \
-e TZ=Asia/Shanghai \
-e SW_OAP_ADDRESS=http://************:12800 \
-v /etc/localtime:/etc/localtime:ro \
swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/apache/skywalking-ui:10.0.1



UOpK4PX$XYYqAZ


TQ3A6L#ZeedPaZV3J


hZAPWHq3&ZmRU3