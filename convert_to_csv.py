import csv

# Define the input file path and output CSV file path
input_file_path = '/Users/<USER>/works/coderepository/local/rag/summary-1.txt'
output_csv_path = '/Users/<USER>/works/coderepository/local/rag/summary.csv'

# Function to parse the content into questions and answers
def parse_content(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # Split content by "###问题:" to separate each issue
    sections = content.split('###问题:')
    qa_pairs = []

    for section in sections[1:]:  # Skip the first empty section after split
        # Separate the question and answer
        try:
            question, answer = section.split('答复：', 1)
            question = question.strip()
            answer = answer.strip()
            qa_pairs.append((question, answer))
        except ValueError:
            print(f"Error parsing section: {section}")

    return qa_pairs

# Write the questions and answers to a CSV file
def write_to_csv(qa_pairs, output_csv_path):
    with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        csv_writer = csv.writer(csvfile)
        # Write header
        csv_writer.writerow(['问题内容', '答复内容'])
        # Write data rows
        for question, answer in qa_pairs:
            csv_writer.writerow([question, answer])

# Parse the input file and write to CSV
qa_pairs = parse_content(input_file_path)
write_to_csv(qa_pairs, output_csv_path)

print(f"CSV file has been created at: {output_csv_path}")