from ragflow_sdk import RAGFlow

rag_object = RAGFlow(api_key="ragflow-Y3M2YzOTZlMWZlNjExZjBhOWUyMDI0Mm", base_url="http://10.15.1.75")
question = "轨迹分段"
dataset_ids= ["061a86f237b511f08bb50242ac1e0306"]
similarity_threshold = 0.2
vector_similarity_weigh = 0.3
top_k = 3
rerank_id = "BAAI/bge-reranker-v2-m3"
keyword = False
highlight = False


retrieves = rag_object.retrieve(question=question, dataset_ids=dataset_ids,top_k=top_k,similarity_threshold=similarity_threshold,vector_similarity_weigh=vector_similarity_weigh,rerank_id=rerank_id)

for  i in retrieves:
    print(i.content)
    print("----------------------")