from openai import OpenAI
import os


# 读取文件内容
def read_file(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        return file.read()
    




def read_file_name(folder_path):

    # 获取文件夹下的所有文件名称
    file_names = os.listdir(folder_path)

    # 过滤出文件（排除文件夹）
    file_names = [f for f in file_names if os.path.isfile(os.path.join(folder_path, f))]

    # 打印文件名称
    for index,file_name in enumerate(file_names):
        if file_name.startswith("2025"):
            print(f"开始执行第{index+1}个文件：{file_name}")
            read_file_content(file_name)
    print(f"已完成所有文件的处理。共计处理{index+1}个文件。")
# 读取文件内容
            


def read_file_content(folder_path,file_path):
    file_content = read_file(folder_path+file_path)

    content = '''<任务>你是一名业务运维专家，你需要充分理解提供的群聊天记录文件中的上下文信息总结提炼并分析其中的售后问题与给出的解决方案或答复。

    <要求>请注意以下几点：

    1、不要使用 markdown 格式；
    2、尽量提供详细信息,例如车牌号、设备号等；
    3、按照示例格式输出,不要增加其他内容；
    4、合并相同的问题,不要单独拆开描述；
    5、只需根据文档内容,不添加任何联想，请确保总结的问题及答复表达清楚，完整自然，如果无法确认，回答“从当前对话中无法判断”；
    6、请注意其中的问题及答复可能跨多轮，你需要在充分理解的基础上将他们进行合理的合并.
    <示例>
    ###问题:D02型号设备轨迹异常 
    答复：平台服务正常，设备网络异常无法与平台通行

    ###问题:xxx 用户投诉设备轨迹异常
    答复：平台服务正常，设备网络异常无法与平台通行'''
    # 使用文件内容进行对话
    response = client.chat.completions.create(
        model=model_name,
        messages=[
            {"role": "system", "content": content},
            {"role": "user", "content": f"请根据要求分析以下文件内容：{file_content}"},
        ],
        stream=False
    )
    print( response)

    print(response.choices[0].message.content)

    with open("summary-moa-20250509.txt", "a", encoding="utf-8") as file:
        file.write(response.choices[0].message.content+"\n")


if __name__ == '__main__':
    # 初始化客户端
    client = OpenAI(api_key="sk-tbsofvfcqbbmbixugvlcsuomxocsnjctmkpngbxnghwmwmdc", base_url="https://api.siliconflow.cn")
    model_name = "deepseek-ai/DeepSeek-R1"
    folder_path = "/Users/<USER>/works/coderepository/local/rag/output/"
    #批量处理
    # read_file_name(folder_path)
    # 单个处理
    file_path = "2025-04-25.txt"
    read_file_content(folder_path,file_path)
